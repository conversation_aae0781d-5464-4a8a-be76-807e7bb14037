using DataHubGatineau.Application.DTOs.Identity;
using DataHubGatineau.Application.Services.Identity.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using DataHubGatineau.Core.Constants;

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// Contrôleur pour la gestion des utilisateurs.
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class UtilisateursController : ControllerBase
{
    private readonly IServiceUtilisateur _serviceUtilisateur;
    private readonly ILogger<UtilisateursController> _logger;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="UtilisateursController"/>.
    /// </summary>
    /// <param name="serviceUtilisateur">Service de gestion des utilisateurs.</param>
    /// <param name="logger">Logger.</param>
    public UtilisateursController(IServiceUtilisateur serviceUtilisateur, ILogger<UtilisateursController> logger)
    {
        _serviceUtilisateur = serviceUtilisateur;
        _logger = logger;
    }

    /// <summary>
    /// Obtient tous les utilisateurs.
    /// </summary>
    /// <returns>Liste des utilisateurs.</returns>
    [HttpGet]
    [Authorize(Policy = "Role:AdministrateurSysteme")]
    public async Task<ActionResult<IEnumerable<UtilisateurDTO>>> ObtenirTous()
    {
        try
        {
            var utilisateurs = await _serviceUtilisateur.ObtenirTousAsync();
            return Ok(utilisateurs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de tous les utilisateurs");
            return StatusCode(500, "Une erreur est survenue lors de la récupération des utilisateurs");
        }
    }

    /// <summary>
    /// Obtient un utilisateur par son identifiant.
    /// </summary>
    /// <param name="id">Identifiant de l'utilisateur.</param>
    /// <returns>L'utilisateur correspondant.</returns>
    [HttpGet("{id}")]
    [Authorize(Policy = "Role:AdministrateurSysteme")]
    public async Task<ActionResult<UtilisateurDTO>> ObtenirParId(Guid id)
    {
        try
        {
            var utilisateur = await _serviceUtilisateur.ObtenirParIdAsync(id);
            if (utilisateur == null)
            {
                return NotFound($"Utilisateur avec l'ID {id} non trouvé");
            }

            return Ok(utilisateur);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de l'utilisateur avec l'ID {UtilisateurId}", id);
            return StatusCode(500, "Une erreur est survenue lors de la récupération de l'utilisateur");
        }
    }

    /// <summary>
    /// Obtient l'utilisateur actuel.
    /// </summary>
    /// <returns>L'utilisateur actuel.</returns>
    [HttpGet("moi")]
    public async Task<ActionResult<UtilisateurDTO>> ObtenirUtilisateurActuel()
    {
        try
        {
            var utilisateurId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(utilisateurId) || !Guid.TryParse(utilisateurId, out var id))
            {
                return Unauthorized("Utilisateur non authentifié");
            }

            var utilisateur = await _serviceUtilisateur.ObtenirParIdAsync(id);
            if (utilisateur == null)
            {
                return NotFound($"Utilisateur avec l'ID {id} non trouvé");
            }

            return Ok(utilisateur);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de l'utilisateur actuel");
            return StatusCode(500, "Une erreur est survenue lors de la récupération de l'utilisateur actuel");
        }
    }

    /// <summary>
    /// Crée un nouvel utilisateur.
    /// </summary>
    /// <param name="demande">Données de l'utilisateur à créer avec rôles et mot de passe.</param>
    /// <returns>L'utilisateur créé.</returns>
    [HttpPost]
    [Authorize(Policy = "Role:AdministrateurSysteme")]
    public async Task<ActionResult<UtilisateurDTO>> Creer([FromBody] DemandeCreationUtilisateurAvecRoles demande)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Créer l'utilisateur de base
            var utilisateurDto = new UtilisateurCreationDTO
            {
                NomUtilisateur = demande.NomUtilisateur,
                Email = demande.Email,
                Prenom = demande.Prenom,
                Nom = demande.Nom
            };

            var nouvelUtilisateur = await _serviceUtilisateur.CreerAsync(utilisateurDto, demande.MotDePasse);

            // Assigner les rôles si spécifiés
            if (demande.IdsRoles != null && demande.IdsRoles.Any())
            {
                foreach (var roleId in demande.IdsRoles)
                {
                    try
                    {
                        await _serviceUtilisateur.AjouterRoleAsync(nouvelUtilisateur.Id, roleId);
                        _logger.LogInformation($"Rôle {roleId} assigné à l'utilisateur {nouvelUtilisateur.NomUtilisateur}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"Erreur lors de l'assignation du rôle {roleId} à l'utilisateur {nouvelUtilisateur.NomUtilisateur}");
                    }
                }
            }

            // Récupérer l'utilisateur avec ses rôles
            var utilisateurAvecRoles = await _serviceUtilisateur.ObtenirParIdAsync(nouvelUtilisateur.Id);
            return CreatedAtAction(nameof(ObtenirParId), new { id = nouvelUtilisateur.Id }, utilisateurAvecRoles);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Erreur de validation lors de la création de l'utilisateur");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de l'utilisateur");
            return StatusCode(500, "Une erreur est survenue lors de la création de l'utilisateur");
        }
    }

    /// <summary>
    /// Met à jour un utilisateur existant.
    /// </summary>
    /// <param name="id">Identifiant de l'utilisateur.</param>
    /// <param name="utilisateur">Données de l'utilisateur à mettre à jour.</param>
    /// <returns>L'utilisateur mis à jour.</returns>
    [HttpPut("{id}")]
    [Authorize(Policy = "Role:AdministrateurSysteme")]
    public async Task<ActionResult<UtilisateurDTO>> MettreAJour(Guid id, [FromBody] UtilisateurMiseAJourDTO utilisateur)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var utilisateurMisAJour = await _serviceUtilisateur.MettreAJourAsync(id, utilisateur);
            return Ok(utilisateurMisAJour);
        }
        catch (KeyNotFoundException ex)
        {
            _logger.LogWarning(ex, "Utilisateur avec l'ID {UtilisateurId} non trouvé", id);
            return NotFound(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Erreur de validation lors de la mise à jour de l'utilisateur avec l'ID {UtilisateurId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour de l'utilisateur avec l'ID {UtilisateurId}", id);
            return StatusCode(500, "Une erreur est survenue lors de la mise à jour de l'utilisateur");
        }
    }

    /// <summary>
    /// Supprime un utilisateur.
    /// </summary>
    /// <param name="id">Identifiant de l'utilisateur.</param>
    /// <returns>Résultat de la suppression.</returns>
    [HttpDelete("{id}")]
    [Authorize(Policy = "Role:AdministrateurSysteme")]
    public async Task<ActionResult> Supprimer(Guid id)
    {
        try
        {
            var resultat = await _serviceUtilisateur.SupprimerAsync(id);
            if (!resultat)
            {
                return NotFound($"Utilisateur avec l'ID {id} non trouvé");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de l'utilisateur avec l'ID {UtilisateurId}", id);
            return StatusCode(500, "Une erreur est survenue lors de la suppression de l'utilisateur");
        }
    }

    /// <summary>
    /// Obtient les rôles d'un utilisateur.
    /// </summary>
    /// <param name="id">Identifiant de l'utilisateur.</param>
    /// <returns>Liste des rôles de l'utilisateur.</returns>
    [HttpGet("{id}/roles")]
    [Authorize(Policy = "Role:AdministrateurSysteme")]
    public async Task<ActionResult<IEnumerable<RoleDTO>>> ObtenirRoles(Guid id)
    {
        try
        {
            var roles = await _serviceUtilisateur.ObtenirRolesUtilisateurAsync(id);
            return Ok(roles);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des rôles de l'utilisateur avec l'ID {UtilisateurId}", id);
            return StatusCode(500, "Une erreur est survenue lors de la récupération des rôles de l'utilisateur");
        }
    }

    /// <summary>
    /// Ajoute un rôle à un utilisateur.
    /// </summary>
    /// <param name="id">Identifiant de l'utilisateur.</param>
    /// <param name="roleId">Identifiant du rôle.</param>
    /// <returns>Résultat de l'ajout.</returns>
    [HttpPost("{id}/roles/{roleId}")]
    [Authorize(Policy = "Role:AdministrateurSysteme")]
    public async Task<ActionResult> AjouterRole(Guid id, Guid roleId)
    {
        try
        {
            var resultat = await _serviceUtilisateur.AjouterRoleAsync(id, roleId);
            if (!resultat)
            {
                return BadRequest("Impossible d'ajouter le rôle à l'utilisateur");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ajout du rôle {RoleId} à l'utilisateur {UtilisateurId}", roleId, id);
            return StatusCode(500, "Une erreur est survenue lors de l'ajout du rôle à l'utilisateur");
        }
    }

    /// <summary>
    /// Supprime un rôle d'un utilisateur.
    /// </summary>
    /// <param name="id">Identifiant de l'utilisateur.</param>
    /// <param name="roleId">Identifiant du rôle.</param>
    /// <returns>Résultat de la suppression.</returns>
    [HttpDelete("{id}/roles/{roleId}")]
    [Authorize(Policy = "Role:AdministrateurSysteme")]
    public async Task<ActionResult> SupprimerRole(Guid id, Guid roleId)
    {
        try
        {
            var resultat = await _serviceUtilisateur.SupprimerRoleAsync(id, roleId);
            if (!resultat)
            {
                return BadRequest("Impossible de supprimer le rôle de l'utilisateur");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression du rôle {RoleId} de l'utilisateur {UtilisateurId}", roleId, id);
            return StatusCode(500, "Une erreur est survenue lors de la suppression du rôle de l'utilisateur");
        }
    }

    /// <summary>
    /// Obtient les groupes d'un utilisateur.
    /// </summary>
    /// <param name="id">Identifiant de l'utilisateur.</param>
    /// <returns>Liste des groupes de l'utilisateur.</returns>
    [HttpGet("{id}/groupes")]
    [Authorize(Policy = "Role:AdministrateurSysteme")]
    public async Task<ActionResult<IEnumerable<GroupeDTO>>> ObtenirGroupes(Guid id)
    {
        try
        {
            var groupes = await _serviceUtilisateur.ObtenirGroupesUtilisateurAsync(id);
            return Ok(groupes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des groupes de l'utilisateur avec l'ID {UtilisateurId}", id);
            return StatusCode(500, "Une erreur est survenue lors de la récupération des groupes de l'utilisateur");
        }
    }
}

/// <summary>
/// DTO pour la création d'un utilisateur avec rôles et mot de passe.
/// </summary>
public class DemandeCreationUtilisateurAvecRoles
{
    /// <summary>
    /// Obtient ou définit le nom d'utilisateur.
    /// </summary>
    [Required]
    [StringLength(50, MinimumLength = 3)]
    public string NomUtilisateur { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit l'adresse e-mail de l'utilisateur.
    /// </summary>
    [Required]
    [EmailAddress]
    [StringLength(100)]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit le prénom de l'utilisateur.
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Prenom { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit le nom de famille de l'utilisateur.
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Nom { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit le mot de passe temporaire (optionnel).
    /// </summary>
    public string? MotDePasse { get; set; }

    /// <summary>
    /// Obtient ou définit si l'utilisateur est actif.
    /// </summary>
    public bool EstActif { get; set; } = true;

    /// <summary>
    /// Obtient ou définit les identifiants des rôles à assigner.
    /// </summary>
    public List<Guid> IdsRoles { get; set; } = new List<Guid>();
}
