using DataHubGatineau.Application.DTOs.Identity;
using DataHubGatineau.Application.Services.Identity.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using DataHubGatineau.Core.Constants;

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// Contrôleur pour la gestion des rôles.
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize(Policy = "Role:AdministrateurSysteme")]
public class RolesController : ControllerBase
{
    private readonly IServiceRole _serviceRole;
    private readonly ILogger<RolesController> _logger;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="RolesController"/>.
    /// </summary>
    /// <param name="serviceRole">Service de gestion des rôles.</param>
    /// <param name="logger">Logger.</param>
    public RolesController(IServiceRole serviceRole, ILogger<RolesController> logger)
    {
        _serviceRole = serviceRole;
        _logger = logger;
    }

    /// <summary>
    /// Obtient tous les rôles.
    /// </summary>
    /// <returns>Liste des rôles.</returns>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<RoleDTO>>> ObtenirTous()
    {
        try
        {
            var roles = await _serviceRole.ObtenirTousAsync();
            return Ok(roles);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de tous les rôles");
            return StatusCode(500, "Une erreur est survenue lors de la récupération des rôles");
        }
    }

    /// <summary>
    /// Obtient un rôle par son identifiant.
    /// </summary>
    /// <param name="id">Identifiant du rôle.</param>
    /// <returns>Le rôle correspondant.</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<RoleDTO>> ObtenirParId(Guid id)
    {
        try
        {
            var role = await _serviceRole.ObtenirParIdAsync(id);
            if (role == null)
            {
                return NotFound($"Rôle avec l'ID {id} non trouvé");
            }

            return Ok(role);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération du rôle avec l'ID {RoleId}", id);
            return StatusCode(500, "Une erreur est survenue lors de la récupération du rôle");
        }
    }

    /// <summary>
    /// Crée un nouveau rôle.
    /// </summary>
    /// <param name="role">Données du rôle à créer.</param>
    /// <returns>Le rôle créé.</returns>
    [HttpPost]
    public async Task<ActionResult<RoleDTO>> Creer([FromBody] RoleCreationDTO role)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var nouveauRole = await _serviceRole.CreerAsync(role);
            return CreatedAtAction(nameof(ObtenirParId), new { id = nouveauRole.Id }, nouveauRole);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Erreur de validation lors de la création du rôle");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création du rôle");
            return StatusCode(500, "Une erreur est survenue lors de la création du rôle");
        }
    }

    /// <summary>
    /// Met à jour un rôle existant.
    /// </summary>
    /// <param name="id">Identifiant du rôle.</param>
    /// <param name="role">Données du rôle à mettre à jour.</param>
    /// <returns>Le rôle mis à jour.</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<RoleDTO>> MettreAJour(Guid id, [FromBody] RoleMiseAJourDTO role)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var roleMisAJour = await _serviceRole.MettreAJourAsync(id, role);
            return Ok(roleMisAJour);
        }
        catch (KeyNotFoundException ex)
        {
            _logger.LogWarning(ex, "Rôle avec l'ID {RoleId} non trouvé", id);
            return NotFound(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Erreur de validation lors de la mise à jour du rôle avec l'ID {RoleId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour du rôle avec l'ID {RoleId}", id);
            return StatusCode(500, "Une erreur est survenue lors de la mise à jour du rôle");
        }
    }

    /// <summary>
    /// Supprime un rôle.
    /// </summary>
    /// <param name="id">Identifiant du rôle.</param>
    /// <returns>Résultat de la suppression.</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult> Supprimer(Guid id)
    {
        try
        {
            var resultat = await _serviceRole.SupprimerAsync(id);
            if (!resultat)
            {
                return NotFound($"Rôle avec l'ID {id} non trouvé");
            }

            return NoContent();
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Erreur de validation lors de la suppression du rôle avec l'ID {RoleId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression du rôle avec l'ID {RoleId}", id);
            return StatusCode(500, "Une erreur est survenue lors de la suppression du rôle");
        }
    }

    /// <summary>
    /// Obtient les utilisateurs ayant un rôle spécifique.
    /// </summary>
    /// <param name="id">Identifiant du rôle.</param>
    /// <returns>Liste des utilisateurs ayant le rôle spécifié.</returns>
    [HttpGet("{id}/utilisateurs")]
    public async Task<ActionResult<IEnumerable<UtilisateurDTO>>> ObtenirUtilisateurs(Guid id)
    {
        try
        {
            var utilisateurs = await _serviceRole.ObtenirUtilisateursParRoleAsync(id);
            return Ok(utilisateurs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des utilisateurs pour le rôle avec l'ID {RoleId}", id);
            return StatusCode(500, "Une erreur est survenue lors de la récupération des utilisateurs");
        }
    }

    /// <summary>
    /// Obtient les permissions d'un rôle.
    /// </summary>
    /// <param name="id">Identifiant du rôle.</param>
    /// <returns>Liste des permissions du rôle.</returns>
    [HttpGet("{id}/permissions")]
    public async Task<ActionResult<IEnumerable<PermissionDTO>>> ObtenirPermissions(Guid id)
    {
        try
        {
            var permissions = await _serviceRole.ObtenirPermissionsRoleAsync(id);
            return Ok(permissions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des permissions pour le rôle avec l'ID {RoleId}", id);
            return StatusCode(500, "Une erreur est survenue lors de la récupération des permissions");
        }
    }

    /// <summary>
    /// Ajoute une permission à un rôle.
    /// </summary>
    /// <param name="id">Identifiant du rôle.</param>
    /// <param name="permissionId">Identifiant de la permission.</param>
    /// <returns>Résultat de l'ajout.</returns>
    [HttpPost("{id}/permissions/{permissionId}")]
    public async Task<ActionResult> AjouterPermission(Guid id, Guid permissionId)
    {
        try
        {
            var resultat = await _serviceRole.AjouterPermissionAsync(id, permissionId);
            if (!resultat)
            {
                return BadRequest("Impossible d'ajouter la permission au rôle");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ajout de la permission {PermissionId} au rôle {RoleId}", permissionId, id);
            return StatusCode(500, "Une erreur est survenue lors de l'ajout de la permission au rôle");
        }
    }

    /// <summary>
    /// Supprime une permission d'un rôle.
    /// </summary>
    /// <param name="id">Identifiant du rôle.</param>
    /// <param name="permissionId">Identifiant de la permission.</param>
    /// <returns>Résultat de la suppression.</returns>
    [HttpDelete("{id}/permissions/{permissionId}")]
    public async Task<ActionResult> SupprimerPermission(Guid id, Guid permissionId)
    {
        try
        {
            var resultat = await _serviceRole.SupprimerPermissionAsync(id, permissionId);
            if (!resultat)
            {
                return BadRequest("Impossible de supprimer la permission du rôle");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de la permission {PermissionId} du rôle {RoleId}", permissionId, id);
            return StatusCode(500, "Une erreur est survenue lors de la suppression de la permission du rôle");
        }
    }
}
