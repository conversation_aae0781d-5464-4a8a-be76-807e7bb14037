using Asp.Versioning;
using DataHubGatineau.API.Services;
using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Entites.Workflow;
using DataHubGatineau.Infrastructure.Persistence;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Security.Claims;
using DataHubGatineau.Domain.Entites.Identity;

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// Contrôleur pour les workflows d'approbation.
/// </summary>
[ApiVersion("2.0")]
[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
public class WorkflowApprobationController : ControllerBase
{
    private readonly CentreDonneesDbContext _context;
    private readonly WorkflowInitializationService _workflowInitializationService;
    private readonly ILogger<WorkflowApprobationController> _logger;

    // Constantes de GUIDs para los estados del workflow
    private static readonly Guid StatutEnAttente = new Guid("11111111-1111-1111-1111-111111111111");
    private static readonly Guid StatutApprouve = new Guid("*************-4444-4444-************");
    private static readonly Guid StatutRejete = new Guid("*************-5555-5555-************");
    private static readonly Guid StatutAnnule = new Guid("*************-8888-8888-************");

    private const int ID_EN_ATTENTE = 1;
    private const int ID_EN_COURS = 2;
    private const int ID_EN_ATTENTE_APPROBATION = 3;
    private const int ID_APPROUVE = 4;
    private const int ID_REJETE = 5;
    private const int ID_COMPLETE = 6;
    private const int ID_ECHOUE = 7;
    private const int ID_ANNULE = 8;

    public WorkflowApprobationController(
        CentreDonneesDbContext context,
        WorkflowInitializationService workflowInitializationService,
        ILogger<WorkflowApprobationController> logger)
    {
        _context = context;
        _workflowInitializationService = workflowInitializationService;
        _logger = logger;
    }

    private async Task EnsureEtapeSchemaAsync()
    {
        // Garantizar columnas necesarias para evitar fallos si la migración formal no fue aplicada aún
        await _context.Database.ExecuteSqlRawAsync(
            "IF COL_LENGTH('Workflow.EtapesWorkflowApprobation', 'EcheanceUtc') IS NULL ALTER TABLE [Workflow].[EtapesWorkflowApprobation] ADD [EcheanceUtc] [datetime2] NULL");
        await _context.Database.ExecuteSqlRawAsync(
            "IF COL_LENGTH('Workflow.EtapesWorkflowApprobation', 'RoleResponsable') IS NULL ALTER TABLE [Workflow].[EtapesWorkflowApprobation] ADD [RoleResponsable] NVARCHAR(200) NULL");

                // Index de performance pour la vue Groupe (filtre par rôle/statut + tri par échéance)
                await _context.Database.ExecuteSqlRawAsync(@"
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_Etapes_Role_Statut_Echeance' AND object_id = OBJECT_ID('Workflow.EtapesWorkflowApprobation'))
BEGIN
    CREATE INDEX [IX_Etapes_Role_Statut_Echeance]
    ON [Workflow].[EtapesWorkflowApprobation]([RoleResponsable],[Statut],[EcheanceUtc])
    INCLUDE([ApprobateurId],[WorkflowApprobationId]);
END");

                // Index utile pour les recherches par approbateur
                await _context.Database.ExecuteSqlRawAsync(@"
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_Etapes_ApprobateurId' AND object_id = OBJECT_ID('Workflow.EtapesWorkflowApprobation'))
BEGIN
    CREATE INDEX [IX_Etapes_ApprobateurId]
    ON [Workflow].[EtapesWorkflowApprobation]([ApprobateurId]);
END");

                // Backfill idempotent pour les enregistrements existants sans rôle responsable
                // Heuristique simple: Ordre 1 -> AnalysteData, Ordre 2 -> GestionnaireData, Ordre >=3 -> AdministrateurSysteme
                // Plus quelques correspondances textuelles sur le nom de l'étape pour améliorer la précision.
                try
                {
                        var roleAnalyste = DataHubGatineau.Core.Constants.Roles.AnalysteData;
                        var roleGestionnaire = DataHubGatineau.Core.Constants.Roles.GestionnaireData;
                        var roleAdmin = DataHubGatineau.Core.Constants.Roles.AdministrateurSysteme;

                        await _context.Database.ExecuteSqlRawAsync(
                                @"UPDATE [Workflow].[EtapesWorkflowApprobation]
                                    SET RoleResponsable = {0}
                                    WHERE RoleResponsable IS NULL AND (Ordre = 1 OR Nom LIKE N'%Analys%' OR Nom LIKE N'%Analy%')",
                                roleAnalyste);

                        await _context.Database.ExecuteSqlRawAsync(
                                @"UPDATE [Workflow].[EtapesWorkflowApprobation]
                                    SET RoleResponsable = {0}
                                    WHERE RoleResponsable IS NULL AND (Ordre = 2 OR Nom LIKE N'%Gestion%')",
                                roleGestionnaire);

                        await _context.Database.ExecuteSqlRawAsync(
                                @"UPDATE [Workflow].[EtapesWorkflowApprobation]
                                    SET RoleResponsable = {0}
                                    WHERE RoleResponsable IS NULL AND (Ordre >= 3 OR Nom LIKE N'%Valid%' OR Nom LIKE N'%Admin%')",
                                roleAdmin);
                }
                catch (Exception ex)
                {
                        _logger.LogWarning(ex, "Backfill RoleResponsable ignoré (non bloquant)");
                }
    }

    private Guid? ObtenirUtilisateurIdDepuisClaims()
    {
        var user = HttpContext?.User;
        if (user == null || user.Identity?.IsAuthenticated != true)
            return null;
        // Essayer plusieurs claims courants (Azure AD, JWT standards)
        var candidates = new[] { "oid", ClaimTypes.NameIdentifier, "sub", "id" };
        foreach (var c in candidates)
        {
            var val = user.FindFirst(c)?.Value;
            if (!string.IsNullOrWhiteSpace(val) && Guid.TryParse(val, out var gid))
                return gid;
        }
        return null;
    }

    private async Task<Utilisateur> AssurerUtilisateurAsync(Guid utilisateurId)
    {
        var exist = await _context.Utilisateurs.FindAsync(utilisateurId);
        if (exist != null) return exist;

        var user = HttpContext?.User;
    var email = user?.FindFirst(ClaimTypes.Email)?.Value
                    ?? user?.FindFirst("emails")?.Value
                    ?? user?.Identity?.Name
                    ?? $"utilisateur-{utilisateurId}@local";
    var nomAffichage = user?.FindFirst(ClaimTypes.Name)?.Value ?? "Utilisateur";

        var username = ((email ?? nomAffichage).Split('@').FirstOrDefault() ?? "utilisateur").Replace(" ", ".").ToLowerInvariant();
        var nouvel = new Utilisateur
        {
            Id = utilisateurId,
            Email = email ?? string.Empty,
            NomUtilisateur = username,
            Nom = nomAffichage ?? "Utilisateur",
            Prenom = string.Empty,
            EstActif = true,
            DateCreation = DateTime.Now,
            DateModification = DateTime.Now,
            CreePar = "System",
            ModifiePar = "System"
        };
        _context.Utilisateurs.Add(nouvel);
        await _context.SaveChangesAsync();
        return nouvel;
    }

    private bool AdminBypassEnabled()
    {
        if (HttpContext?.RequestServices.GetService(typeof(IConfiguration)) is IConfiguration cfg)
        {
            return cfg.GetValue<bool?>("Workflow:AllowAdminBypass") ?? false;
        }
        return false;
    }

    private bool EstAdministrateur(Guid uid)
    {
        var roleNom = DataHubGatineau.Core.Constants.Roles.AdministrateurSysteme;
        var role = _context.Roles.FirstOrDefault(r => r.Nom == roleNom);
        if (role == null) return false;
        var direct = _context.UtilisateursRoles.Any(ur => ur.RoleId == role.Id && ur.UtilisateurId == uid);
        if (direct) return true;
        var viaGroupe = _context.GroupesRoles
            .Where(gr => gr.RoleId == role.Id)
            .Join(_context.UtilisateursGroupes, gr => gr.GroupeId, ug => ug.GroupeId, (gr, ug) => ug)
            .Any(ug => ug.UtilisateurId == uid);
        return viaGroupe;
    }

    private async Task<Guid> ObtenirIdStatutParNom(string nom)
    {
        // Convertir a mayúsculas y normalizar
        var nomNormalise = nom
            .ToUpper()
            .Replace(" ", "_")
            .Normalize(NormalizationForm.FormD)
            .Where(c => CharUnicodeInfo.GetUnicodeCategory(c) != UnicodeCategory.NonSpacingMark)
            .Aggregate(string.Empty, (current, c) => current + c);

        // Buscar primero por Nom
        var statut = await _context.WorkflowStatuts
            .FirstOrDefaultAsync(s => s.Nom == nomNormalise);

        if (statut == null)
        {
            // Si no se encuentra, buscar por Libelle
            statut = await _context.WorkflowStatuts
                .FirstOrDefaultAsync(s => 
                    s.Libelle.ToUpper()
                        .Replace(" ", "_")
                        .Normalize(NormalizationForm.FormD)
                        .Where(c => CharUnicodeInfo.GetUnicodeCategory(c) != UnicodeCategory.NonSpacingMark)
                        .Aggregate(string.Empty, (current, c) => current + c) == nomNormalise);
        }

        if (statut == null)
        {
            throw new InvalidOperationException($"Statut non trouvé: {nom} (normalizado: {nomNormalise})");
        }
        return statut.Id;
    }

    /// <summary>
    /// Initialise les données nécessaires pour le workflow d'approbation.
    /// </summary>
    [HttpPost("initialiser")]
    public async Task<IActionResult> InitialiserDonneesWorkflow()
    {
        try
        {
            await _workflowInitializationService.InitialiserDonneesWorkflowAsync();
            return Ok(new { message = "Données de workflow initialisées avec succès" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'initialisation des données de workflow");
            return StatusCode(500, "Erreur lors de l'initialisation des données de workflow");
        }
    }

    /// <summary>
    /// Obtient tous les statuts disponibles pour les workflows.
    /// </summary>
    [HttpGet("statuts")]
    public async Task<IActionResult> ObtenirStatuts()
    {
        try
        {
            var statuts = await _context.WorkflowStatuts
                .OrderBy(s => s.Ordre)
                .Select(s => new
                {
                    Id = s.Id,
                    Nom = s.Nom,
                    Description = s.Description,
                    Ordre = s.Ordre
                })
                .ToListAsync();

            return Ok(statuts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention des statuts");
            return StatusCode(500, new
            {
                Success = false,
                Message = "Erreur lors de l'obtention des statuts",
                Error = ex.Message
            });
        }
    }

    /// <summary>
    /// Retourne la liste des utilisateurs assignés à un rôle donné (par nom de rôle).
    /// </summary>
    [HttpGet("utilisateurs-par-role")]
    public async Task<IActionResult> ObtenirUtilisateursParRole([FromQuery] string role)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(role))
            {
                return BadRequest(new { Success = false, Message = "Paramètre 'role' obligatoire" });
            }

            var roleEntite = await _context.Roles.FirstOrDefaultAsync(r => r.Nom == role);
            if (roleEntite == null)
            {
                return Ok(Enumerable.Empty<object>());
            }

            var directs = _context.UtilisateursRoles
                .Where(ur => ur.RoleId == roleEntite.Id)
                .Join(_context.Utilisateurs,
                    ur => ur.UtilisateurId,
                    u => u.Id,
                    (ur, u) => u)
                .Where(u => u.EstActif);

            var viaGroupes = _context.GroupesRoles
                .Where(gr => gr.RoleId == roleEntite.Id)
                .Join(_context.UtilisateursGroupes, gr => gr.GroupeId, ug => ug.GroupeId, (gr, ug) => ug.UtilisateurId)
                .Join(_context.Utilisateurs, uid => uid, u => u.Id, (uid, u) => u)
                .Where(u => u.EstActif);

            var utilisateurs = await directs
                .Union(viaGroupes)
                .Select(u => new
                {
                    u.Id,
                    u.Nom,
                    u.Prenom,
                    u.Email,
                    NomComplet = (u.Prenom + " " + u.Nom).Trim()
                })
                .OrderBy(u => u.Nom)
                .ThenBy(u => u.Prenom)
                .ToListAsync();

            return Ok(utilisateurs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention des utilisateurs pour le rôle {Role}", role);
            return StatusCode(500, new { Success = false, Message = "Erreur lors de l'obtention des utilisateurs par rôle", Error = ex.Message });
        }
    }

    /// <summary>
    /// Crée un nouveau workflow d'approbation.
    /// </summary>
    [HttpPost("creer")]
    public async Task<IActionResult> CreerWorkflow([FromBody] CreerWorkflowRequest request)
    {
        try
        {
            // S'assurer que le schéma des étapes est à jour (colonnes, index) avant de créer
            await EnsureEtapeSchemaAsync();
            _logger.LogInformation($"Création d'un nouveau workflow avec type: {request.Type}");

            // Déterminer l'initiateur: préférer l'utilisateur authentifié; sinon, utiliser la valeur de la requête
            var utilisateurId = ObtenirUtilisateurIdDepuisClaims() ?? (request.InitiateurId != Guid.Empty ? request.InitiateurId : Guid.Empty);
            if (utilisateurId == Guid.Empty)
            {
                return Unauthorized(new { Success = false, Message = "Utilisateur non authentifié ou InitiateurId manquant" });
            }
            // S'assurer que l'utilisateur existe ou le créer à partir des claims
            var utilisateur = await AssurerUtilisateurAsync(utilisateurId);

            // Vérifier que l'état initial existe
            var statut = await _context.WorkflowStatuts.FindAsync(StatutEnAttente);
            if (statut == null)
            {
                await _workflowInitializationService.InitialiserDonneesWorkflowAsync();
                statut = await _context.WorkflowStatuts.FindAsync(StatutEnAttente);
                if (statut == null)
                {
                    throw new InvalidOperationException("État initial non trouvé après initialisation");
                }
            }

            // Garde serveur: empêcher plusieurs workflows "en attente" pour le même objet
            if (!string.IsNullOrWhiteSpace(request.ObjetType) && request.ObjetId.HasValue)
            {
                if (!Enum.TryParse<ObjetType>(request.ObjetType, ignoreCase: true, out var objetTypeEnum))
                {
                    return BadRequest(new
                    {
                        Success = false,
                        Message = $"ObjetType invalide: {request.ObjetType}. Valeurs permises: {string.Join(", ", Enum.GetNames(typeof(ObjetType)))}"
                    });
                }

                // Récupérer les workflows déjà associés à cet objet
                var assocWorkflowIds = await _context.WorkflowsObjetsAssocies
                    .Where(a => a.ObjetType == objetTypeEnum && a.ObjetId == request.ObjetId.Value)
                    .Select(a => a.WorkflowId)
                    .Distinct()
                    .ToListAsync();

                if (assocWorkflowIds.Any())
                {
                    var workflowsAssocies = await _context.WorkflowsApprobation
                        .Include(w => w.Statut)
                        .Where(w => assocWorkflowIds.Contains(w.Id))
                        .ToListAsync();

                    bool EstEnAttente(WorkflowStatut? s)
                        => s != null && (
                               s.Nom.Equals("EN_ATTENTE", StringComparison.OrdinalIgnoreCase)
                            || s.Nom.Equals("EN_ATTENTE_APPROBATION", StringComparison.OrdinalIgnoreCase)
                            || s.Nom.Contains("EN_ATTENTE", StringComparison.OrdinalIgnoreCase)
                            || s.Nom.Equals("PENDING", StringComparison.OrdinalIgnoreCase)
                        );

                    var existant = workflowsAssocies.FirstOrDefault(w => EstEnAttente(w.Statut));
                    if (existant != null)
                    {
                        return Conflict(new
                        {
                            Success = false,
                            Code = "WORKFLOW_EN_COURS",
                            Message = "Un workflow est déjà en attente pour cet objet.",
                            Details = new { existant.Id, request.ObjetType, request.ObjetId }
                        });
                    }
                }
            }

            // Valeurs par défaut si non fournies
            var nomDefaut = string.IsNullOrWhiteSpace(request.Nom)
                ? $"Approbation {(string.IsNullOrWhiteSpace(request.ObjetType) ? "Objet" : request.ObjetType)}"
                : request.Nom!;
            var typeDefaut = string.IsNullOrWhiteSpace(request.Type) ? "APPROBATION" : request.Type!.ToUpperInvariant();

            // Créer le workflow avec l'état initial correct
            var workflow = new WorkflowApprobation
            {
                Id = Guid.NewGuid(),
                Nom = nomDefaut,
                Description = request.Description,
                InitiateurId = utilisateur.Id,
                StatutId = StatutEnAttente,
                DateSoumission = DateTime.Now,
                DateCreation = DateTime.Now,
                DateModification = DateTime.Now,
                CreePar = string.IsNullOrWhiteSpace($"{utilisateur.Prenom} {utilisateur.Nom}".Trim()) 
                    ? utilisateur.NomUtilisateur ?? utilisateur.Email ?? "System"
                    : $"{utilisateur.Prenom} {utilisateur.Nom}".Trim(),
                ModifiePar = string.IsNullOrWhiteSpace($"{utilisateur.Prenom} {utilisateur.Nom}".Trim())
                    ? utilisateur.NomUtilisateur ?? utilisateur.Email ?? "System"
                    : $"{utilisateur.Prenom} {utilisateur.Nom}".Trim()
            };

            _context.WorkflowsApprobation.Add(workflow);
            await _context.SaveChangesAsync();

            // Créer les étapes du workflow
            try
            {
                await _workflowInitializationService.CreerEtapesWorkflowAsync(workflow.Id);
            }
            catch (InvalidOperationException ex)
            {
                // Supprimer le workflow créé si la création des étapes échoue pour cause de rôles manquants
                _context.WorkflowsApprobation.Remove(workflow);
                await _context.SaveChangesAsync();
                return BadRequest(new
                {
                    Success = false,
                    Code = "ROLES_MANQUANTS",
                    Message = ex.Message,
                    Details = new { Requis = new[] { "AnalysteData", "GestionnaireData", "AdministrateurSysteme" } }
                });
            }

            // Définir l'échéance (SLA) de la première étape en attente immédiatement après la création
            try
            {
                var workflowsAvecEtapes = await _context.WorkflowsApprobation
                    .Include(w => w.Etapes)
                    .FirstOrDefaultAsync(w => w.Id == workflow.Id);
                var premiereEtape = workflowsAvecEtapes?.Etapes
                    .OrderBy(e => e.Ordre)
                    .FirstOrDefault(e => e.Statut == "EN_ATTENTE");
                if (premiereEtape != null)
                {
                    var joursParDefaut = HttpContext.RequestServices.GetService(typeof(IConfiguration)) is IConfiguration cfg
                        ? (cfg.GetValue<int?>("Workflow:Sla:JoursParDefaut") ?? 5)
                        : 5;
                    premiereEtape.EcheanceUtc = DateTime.UtcNow.AddDays(joursParDefaut);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Impossible de définir l'échéance initiale de la première étape (non bloquant)");
            }

            // Associer au besoin ce workflow à un objet polymorphe
            if (!string.IsNullOrWhiteSpace(request.ObjetType) && request.ObjetId.HasValue)
            {
                if (!Enum.TryParse<ObjetType>(request.ObjetType, ignoreCase: true, out var objetTypeEnum))
                {
                    return BadRequest(new
                    {
                        Success = false,
                        Message = $"ObjetType invalide: {request.ObjetType}. Valeurs permises: {string.Join(", ", Enum.GetNames(typeof(ObjetType)))}"
                    });
                }

                var association = new WorkflowsObjetsAssocies
                {
                    Id = Guid.NewGuid(),
                    WorkflowId = workflow.Id,
                    ObjetType = objetTypeEnum,
                    ObjetId = request.ObjetId.Value,
                    DateAssociation = DateTime.UtcNow,
                        CreePar = "System",
                        EstEnAttente = true,
                    ModifiePar = "System",
                    DateCreation = DateTime.UtcNow,
                    DateModification = DateTime.UtcNow
                };

                _context.WorkflowsObjetsAssocies.Add(association);
                try
                {
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateException dbEx)
                {
                    // Gérer les conflits d'unicité (association dupliquée)
                    var message = dbEx.InnerException?.Message ?? dbEx.Message;
                    if (message.Contains("IX_WorkflowsObjetsAssocies_WorkflowId_ObjetType_ObjetId", StringComparison.OrdinalIgnoreCase)
                        || message.Contains("UNIQUE", StringComparison.OrdinalIgnoreCase)
                        || message.Contains("duplicate", StringComparison.OrdinalIgnoreCase))
                    {
                        _logger.LogWarning(dbEx, "Conflit d'association unique pour Workflow {WorkflowId} -> {ObjetType} {ObjetId}", workflow.Id, objetTypeEnum, request.ObjetId);
                        return Conflict(new
                        {
                            Success = false,
                            Code = "ASSOCIATION_DUPLIQUEE",
                            Message = "Ce workflow est déjà associé à cet objet.",
                            Details = new { workflow.Id, ObjetType = objetTypeEnum.ToString(), request.ObjetId }
                        });
                    }
                    throw; // autre erreur DB
                }
            }

            return Ok(workflow);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création du workflow");
            return StatusCode(500, new
            {
                Success = false,
                Message = "Erreur lors de la création du workflow",
                Error = ex.Message
            });
        }
    }

    /// <summary>
    /// Réassigne l'approbateur d'une étape en attente d'un workflow.
    /// </summary>
    [HttpPost("{workflowId}/etapes/{etapeId}/reassigner")]
    public async Task<IActionResult> ReassignerEtape(Guid workflowId, Guid etapeId, [FromBody] ReassignerEtapeRequest request)
    {
        try
        {
            await EnsureEtapeSchemaAsync();
            var workflow = await _context.WorkflowsApprobation
                .Include(w => w.Etapes)
                .FirstOrDefaultAsync(w => w.Id == workflowId);

            if (workflow == null)
            {
                return NotFound(new { Success = false, Message = $"Workflow {workflowId} non trouvé" });
            }

            var etape = workflow.Etapes.FirstOrDefault(e => e.Id == etapeId);
            if (etape == null)
            {
                return NotFound(new { Success = false, Message = $"Étape {etapeId} non trouvée" });
            }

            // Autoriser la réassignation seulement si l'étape est en attente
            bool EnAttente(string s) =>
                !string.IsNullOrWhiteSpace(s) && (
                    s.Equals("EN_ATTENTE", StringComparison.OrdinalIgnoreCase)
                    || s.Equals("EN_ATTENTE_APPROBATION", StringComparison.OrdinalIgnoreCase)
                    || s.Contains("EN_ATTENTE", StringComparison.OrdinalIgnoreCase)
                    || s.Equals("En attente", StringComparison.OrdinalIgnoreCase)
                    || s.Contains("attente", StringComparison.OrdinalIgnoreCase)
                );

            if (!EnAttente(etape.Statut))
            {
                return BadRequest(new { Success = false, Message = "Impossible de réassigner une étape non en attente." });
            }

            etape.ApprobateurId = request.NouvelApprobateurId;
            etape.DateApprobation = null;
            etape.DateModification = DateTime.Now;
            if (!string.IsNullOrWhiteSpace(request.Commentaire))
            {
                var ts = DateTime.Now.ToString("yyyy-MM-dd HH:mm");
                etape.Commentaires = string.IsNullOrWhiteSpace(etape.Commentaires)
                    ? $"[{ts}] Réassignée: {request.Commentaire}"
                    : $"{etape.Commentaires}\n[{ts}] Réassignée: {request.Commentaire}";
            }

            workflow.DateModification = DateTime.Now;
            await _context.SaveChangesAsync();

            // Marquer les associations comme non en attente (workflow rejeté)
            var assocs = await _context.WorkflowsObjetsAssocies
                .Where(a => a.WorkflowId == workflowId && a.EstEnAttente)
                .ToListAsync();
            if (assocs.Any())
            {
                foreach (var a in assocs)
                {
                    a.EstEnAttente = false;
                    a.DateModification = DateTime.Now;
                    a.ModifiePar = "System";
                }
                await _context.SaveChangesAsync();
            }

            return Ok(new { Success = true });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la réassignation de l'étape {EtapeId} du workflow {WorkflowId}", etapeId, workflowId);
            return StatusCode(500, new { Success = false, Message = "Erreur lors de la réassignation", Error = ex.Message });
        }
    }

    /// <summary>
    /// Permet à un membre du rôle responsable de "prendre" (claim) une étape en attente.
    /// </summary>
    [HttpPost("{workflowId}/etapes/{etapeId}/prendre")]
    public async Task<IActionResult> PrendreEtape(Guid workflowId, Guid etapeId, [FromBody] PrendreEtapeRequest request)
    {
        try
        {
            await EnsureEtapeSchemaAsync();
            var workflow = await _context.WorkflowsApprobation
                .Include(w => w.Etapes)
                .FirstOrDefaultAsync(w => w.Id == workflowId);
            if (workflow == null)
                return NotFound(new { Success = false, Message = $"Workflow {workflowId} non trouvé" });

            var etape = workflow.Etapes.FirstOrDefault(e => e.Id == etapeId);
            if (etape == null)
                return NotFound(new { Success = false, Message = $"Étape {etapeId} non trouvée" });

            bool EnAttente(string s) => !string.IsNullOrWhiteSpace(s) && (s.Equals("EN_ATTENTE", StringComparison.OrdinalIgnoreCase)
                || s.Equals("EN_ATTENTE_APPROBATION", StringComparison.OrdinalIgnoreCase) || s.Contains("EN_ATTENTE", StringComparison.OrdinalIgnoreCase));

            if (!EnAttente(etape.Statut))
                return BadRequest(new { Success = false, Message = "Étape non en attente" });

            // Autoriser seulement si l'utilisateur est membre du rôle responsable de l'étape
            var roleNom = etape.RoleResponsable;
            if (string.IsNullOrWhiteSpace(roleNom))
                return BadRequest(new { Success = false, Message = "Étape sans rôle responsable défini" });

            var role = await _context.Roles.FirstOrDefaultAsync(r => r.Nom == roleNom);
            if (role == null)
                return BadRequest(new { Success = false, Message = $"Rôle responsable introuvable: {roleNom}" });

            var estMembreDirect = await _context.UtilisateursRoles.AnyAsync(ur => ur.RoleId == role.Id && ur.UtilisateurId == request.UtilisateurId);
            var estMembreParGroupe = await _context.GroupesRoles
                .Where(gr => gr.RoleId == role.Id)
                .Join(_context.UtilisateursGroupes, gr => gr.GroupeId, ug => ug.GroupeId, (gr, ug) => ug)
                .AnyAsync(ug => ug.UtilisateurId == request.UtilisateurId);
            if (!estMembreDirect && !estMembreParGroupe)
            {
                if (AdminBypassEnabled() && EstAdministrateur(request.UtilisateurId))
                {
                    _logger.LogWarning("Bypass administrateur accordé pour la prise de l'étape {EtapeId} du workflow {WorkflowId} par {UtilisateurId}", etapeId, workflowId, request.UtilisateurId);
                }
                else
                {
                    _logger.LogWarning("403 ROLE_REQUIS lors de la prise d'étape: workflow={WorkflowId}, etape={EtapeId}, uid={UtilisateurId}, roleRequis={Role}", workflowId, etapeId, request.UtilisateurId, roleNom);
                    return StatusCode(403, new { Success = false, Code = "ROLE_REQUIS", Message = "Vous n’avez plus le rôle requis pour cette étape.", RequiredRole = roleNom });
                }
            }

            etape.ApprobateurId = request.UtilisateurId;
            etape.DateModification = DateTime.Now;
            workflow.DateModification = DateTime.Now;
            await _context.SaveChangesAsync();

            return Ok(new { Success = true });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la prise de l'étape {EtapeId}", etapeId);
            return StatusCode(500, new { Success = false, Message = "Erreur lors de la prise de l'étape", Error = ex.Message });
        }
    }

    /// <summary>
    /// Obtient tous les workflows d'approbation.
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> ObtenirTous()
    {
        try
        {
            await EnsureEtapeSchemaAsync();
            var workflows = await _context.WorkflowsApprobation
                .Include(w => w.Etapes)
                    .ThenInclude(e => e.Approbateur)
                .Include(w => w.Initiateur)
                .Include(w => w.Statut)
                .ToListAsync();

            return Ok(workflows);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des workflows");
            return StatusCode(500, new
            {
                Success = false,
                Message = "Erreur lors de la récupération des workflows",
                Error = ex.Message
            });
        }
    }

    /// <summary>
    /// Obtient un workflow par son ID.
    /// </summary>
    [HttpGet("{id}")]
    public async Task<IActionResult> ObtenirParId(Guid id)
    {
        try
        {
            await EnsureEtapeSchemaAsync();
            var workflow = await _context.WorkflowsApprobation
                .Include(w => w.Etapes)
                    .ThenInclude(e => e.Approbateur)
                .Include(w => w.Initiateur)
                .Include(w => w.Statut)
                .FirstOrDefaultAsync(w => w.Id == id);

            if (workflow == null)
            {
                return NotFound($"Workflow {id} non trouvé");
            }

            return Ok(workflow);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Erreur lors de la récupération du workflow {id}");
            return StatusCode(500, new
            {
                Success = false,
                Message = "Erreur lors de la récupération du workflow",
                Error = ex.Message
            });
        }
    }

    /// <summary>
    /// Obtient les workflows par actif de données.
    /// </summary>
    [HttpGet("actif/{actifId}")]
    public IActionResult ObtenirParActif(Guid actifId)
    {
        // Cette méthode n'est plus valide car la liaison directe avec ActifDonneesId a été supprimée.
        // Il faut utiliser la table d'association WorkflowsObjetsAssocies pour retrouver les workflows liés à un objet.
        return StatusCode(410, new { Message = "Cette méthode n'est plus supportée. Utilisez la nouvelle logique d'association flexible." });
    }

    /// <summary>
    /// Obtient les workflows associés à un objet polymorphe (ex.: ActifDonnees, TermeGlossaire).
    /// </summary>
    [HttpGet("objets/{objetType}/{objetId}")]
    public async Task<IActionResult> ObtenirParObjet(string objetType, Guid objetId)
    {
        try
        {
            await EnsureEtapeSchemaAsync();
            if (!Enum.TryParse<ObjetType>(objetType, ignoreCase: true, out var objetTypeEnum))
            {
                return BadRequest(new
                {
                    Success = false,
                    Message = $"ObjetType invalide: {objetType}. Valeurs permises: {string.Join(", ", Enum.GetNames(typeof(ObjetType)))}"
                });
            }

            var workflowIds = await _context.WorkflowsObjetsAssocies
                .Where(a => a.ObjetType == objetTypeEnum && a.ObjetId == objetId)
                .Select(a => a.WorkflowId)
                .Distinct()
                .ToListAsync();

            if (!workflowIds.Any())
            {
                return Ok(Enumerable.Empty<object>());
            }

            var workflows = await _context.WorkflowsApprobation
                .Include(w => w.Etapes)
                    .ThenInclude(e => e.Approbateur)
                .Include(w => w.Initiateur)
                .Include(w => w.Statut)
                .Where(w => workflowIds.Contains(w.Id))
                .ToListAsync();

            return Ok(workflows);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Erreur lors de la récupération des workflows pour {objetType}/{objetId}");
            return StatusCode(500, new
            {
                Success = false,
                Message = "Erreur lors de la récupération des workflows",
                Error = ex.Message
            });
        }
    }

    /// <summary>
    /// Obtient les workflows d'approbation assignés à un utilisateur (par initiateur ou approbateur d'étape).
    /// </summary>
    [HttpGet("instances/parAssigne/{utilisateurId}")]
    public async Task<IActionResult> ObtenirParAssigne(Guid utilisateurId)
    {
        try
        {
            await EnsureEtapeSchemaAsync();
            // Workflows où l'utilisateur est l'initiateur ou approbateur d'une étape
            var workflows = await _context.WorkflowsApprobation
                .Include(w => w.Etapes)
                    .ThenInclude(e => e.Approbateur)
                .Include(w => w.Initiateur)
                .Include(w => w.Statut)
                .Where(w => w.InitiateurId == utilisateurId
                    || w.Etapes.Any(e => e.ApprobateurId == utilisateurId))
                .ToListAsync();

            return Ok(workflows);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Erreur lors de la récupération des workflows assignés à l'utilisateur {utilisateurId}");
            return StatusCode(500, new
            {
                Success = false,
                Message = "Erreur lors de la récupération des workflows assignés",
                Error = ex.Message
            });
        }
    }

    /// <summary>
    /// Retourne les workflows dont la prochaine étape en attente est claimable par au moins un des rôles fournis.
    /// Optionnellement, on ne retient que les étapes non assignées ou déjà assignées à l'utilisateur indiqué (vue groupe).
    /// </summary>
    /// <param name="roles">Liste de noms de rôles séparés par des virgules (ex.: AnalysteData,GestionnaireData)</param>
    /// <param name="utilisateurId">Identifiant de l'utilisateur courant pour inclure les étapes déjà assignées à lui.</param>
    /// <param name="page">Numéro de la page pour la pagination (défaut: 1)</param>
    /// <param name="taillePage">Taille de la page pour la pagination (défaut: 10)</param>
    [HttpGet("instances/parRoles")]
    public async Task<IActionResult> ObtenirParRoles([FromQuery] string roles, [FromQuery] Guid? utilisateurId, [FromQuery] int page = 1, [FromQuery] int taillePage = 10)
    {
        try
        {
            await EnsureEtapeSchemaAsync();
            if (string.IsNullOrWhiteSpace(roles))
            {
                return Ok(Enumerable.Empty<object>());
            }

            var setRoles = roles
                .Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries)
                .ToHashSet(StringComparer.OrdinalIgnoreCase);

            if (!setRoles.Any())
            {
                return Ok(Enumerable.Empty<object>());
            }

            // Statuts considérés comme "en attente"
            var statutsEnAttente = new[] { "EN_ATTENTE", "EN_ATTENTE_APPROBATION" };

            var query = _context.WorkflowsApprobation
                .Include(w => w.Etapes)
                    .ThenInclude(e => e.Approbateur)
                .Include(w => w.Initiateur)
                .Include(w => w.Statut)
                .Where(w => w.Etapes.Any(e =>
                    // L'étape doit être dans un état d'attente
                    statutsEnAttente.Contains(e.Statut) &&
                    // Le rôle responsable doit être dans la liste fournie
                    e.RoleResponsable != null && setRoles.Contains(e.RoleResponsable) &&
                    // Condition sur l'assignation
                    (
                        // Si un utilisateur est fourni, on prend les étapes non assignées OU assignées à lui
                        (utilisateurId.HasValue && utilisateurId.Value != Guid.Empty && (e.ApprobateurId == Guid.Empty || e.ApprobateurId == utilisateurId.Value)) ||
                        // Si aucun utilisateur n'est fourni, on ne prend que les étapes non assignées
                        ((!utilisateurId.HasValue || utilisateurId.Value == Guid.Empty) && (e.ApprobateurId == Guid.Empty))
                    )
                ));

            // Ajout du tri par date d'échéance la plus proche
            var workflowsFiltres = await query
                .OrderBy(w => w.Etapes
                    .Where(e => statutsEnAttente.Contains(e.Statut))
                    .Min(e => e.EcheanceUtc)) // Trier par l'échéance la plus proche
                .Skip((page - 1) * taillePage)
                .Take(taillePage)
                .ToListAsync();

            return Ok(workflowsFiltres);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention des workflows par rôles");
            return StatusCode(500, new { Success = false, Message = "Erreur lors de l'obtention des workflows par rôles", Error = ex.Message });
        }
    }

    /// <summary>
    /// Variante paginée qui retourne un contrat { items, total, page, pageSize } sans casser l'endpoint existant.
    /// </summary>
    [HttpGet("instances/parRoles/pagine")]
    public async Task<IActionResult> ObtenirParRolesPagine([FromQuery] string roles, [FromQuery] Guid? utilisateurId, [FromQuery] int page = 1, [FromQuery] int taillePage = 10)
    {
        try
        {
            await EnsureEtapeSchemaAsync();
            if (string.IsNullOrWhiteSpace(roles))
            {
                return Ok(new { items = Array.Empty<object>(), total = 0, page, pageSize = taillePage });
            }

            var setRoles = roles
                .Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries)
                .ToHashSet(StringComparer.OrdinalIgnoreCase);

            if (!setRoles.Any())
            {
                return Ok(new { items = Array.Empty<object>(), total = 0, page, pageSize = taillePage });
            }

            var statutsEnAttente = new[] { "EN_ATTENTE", "EN_ATTENTE_APPROBATION" };

            var baseQuery = _context.WorkflowsApprobation
                .Where(w => w.Etapes.Any(e =>
                    statutsEnAttente.Contains(e.Statut) &&
                    e.RoleResponsable != null && setRoles.Contains(e.RoleResponsable) &&
                    (
                        (utilisateurId.HasValue && utilisateurId.Value != Guid.Empty && (e.ApprobateurId == Guid.Empty || e.ApprobateurId == utilisateurId.Value)) ||
                        ((!utilisateurId.HasValue || utilisateurId.Value == Guid.Empty) && (e.ApprobateurId == Guid.Empty))
                    )
                ));

            var total = await baseQuery.CountAsync();

            var items = await _context.WorkflowsApprobation
                .Include(w => w.Etapes)
                    .ThenInclude(e => e.Approbateur)
                .Include(w => w.Initiateur)
                .Include(w => w.Statut)
                .Where(w => baseQuery.Select(b => b.Id).Contains(w.Id))
                .OrderBy(w => w.Etapes
                    .Where(e => statutsEnAttente.Contains(e.Statut))
                    .Min(e => e.EcheanceUtc))
                .Skip((page - 1) * taillePage)
                .Take(taillePage)
                .ToListAsync();

            return Ok(new { items, total, page, pageSize = taillePage });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention paginée des workflows par rôles");
            return StatusCode(500, new { Success = false, Message = "Erreur lors de l'obtention paginée des workflows par rôles", Error = ex.Message });
        }
    }

    /// <summary>
    /// Obtient les objets associés à un workflow (association polymorphe).
    /// </summary>
    [HttpGet("{workflowId}/associations")]
    public async Task<IActionResult> ObtenirAssociationsParWorkflow(Guid workflowId)
    {
        try
        {
            // Vérifier que le workflow existe
            var existe = await _context.WorkflowsApprobation.AnyAsync(w => w.Id == workflowId);
            if (!existe)
            {
                return NotFound(new { Success = false, Message = $"Workflow {workflowId} non trouvé" });
            }

            var associations = await _context.WorkflowsObjetsAssocies
                .Where(a => a.WorkflowId == workflowId)
                .Select(a => new
                {
                    a.ObjetType,
                    a.ObjetId
                })
                .ToListAsync();

            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention des associations pour le workflow {WorkflowId}", workflowId);
            return StatusCode(500, new
            {
                Success = false,
                Message = "Erreur lors de l'obtention des associations",
                Error = ex.Message
            });
        }
    }

    /// <summary>
    /// Ajoute un commentaire générique à un workflow d'approbation.
    /// Le commentaire est enregistré sur l'étape en attente si disponible, sinon sur la dernière étape.
    /// </summary>
    [HttpPost("{workflowId}/commentaires")]
    public async Task<IActionResult> AjouterCommentaire(Guid workflowId, [FromBody] AjouterCommentaireRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.Contenu))
            {
                return BadRequest("Le contenu du commentaire est obligatoire");
            }

            var workflow = await _context.WorkflowsApprobation
                .Include(w => w.Etapes)
                .FirstOrDefaultAsync(w => w.Id == workflowId);

            if (workflow == null)
            {
                return NotFound($"Workflow {workflowId} non trouvé");
            }

            // Étape cible: en attente si possible, sinon la dernière par ordre
            var etapeCible = workflow.Etapes
                .OrderBy(e => e.Ordre)
                .FirstOrDefault(e => e.Statut == "EN_ATTENTE")
                ?? workflow.Etapes.OrderByDescending(e => e.Ordre).FirstOrDefault();

            if (etapeCible == null)
            {
                return BadRequest("Aucune étape trouvée pour attacher le commentaire");
            }

            var horodatage = DateTime.Now;
            var prefix = string.IsNullOrWhiteSpace(request.UtilisateurNom)
                ? $"[{horodatage:yyyy-MM-dd HH:mm}]"
                : $"[{horodatage:yyyy-MM-dd HH:mm}] {request.UtilisateurNom}:";

            // Concaténer proprement
            etapeCible.Commentaires = string.IsNullOrWhiteSpace(etapeCible.Commentaires)
                ? $"{prefix} {request.Contenu}"
                : $"{etapeCible.Commentaires}\n{prefix} {request.Contenu}";

            workflow.DateModification = DateTime.Now;
            workflow.ModifiePar = request.UtilisateurNom ?? "System";

            await _context.SaveChangesAsync();

            // Marquer les associations comme non en attente (workflow annulé)
            var assocs = await _context.WorkflowsObjetsAssocies
                .Where(a => a.WorkflowId == workflowId && a.EstEnAttente)
                .ToListAsync();
            if (assocs.Any())
            {
                foreach (var a in assocs)
                {
                    a.EstEnAttente = false;
                    a.DateModification = DateTime.Now;
                    a.ModifiePar = "System";
                }
                await _context.SaveChangesAsync();
            }

            // Retourner une forme compatible avec le modèle frontend WorkflowCommentaire
            return Ok(new
            {
                Id = Guid.NewGuid(),
                Contenu = request.Contenu,
                UtilisateurId = request.UtilisateurId,
                UtilisateurNom = request.UtilisateurNom,
                DateCreation = horodatage,
                WorkflowInstanceId = 0 // compatibilité (v1 utilisait un int)
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ajout d'un commentaire au workflow {WorkflowId}", workflowId);
            return StatusCode(500, new
            {
                Success = false,
                Message = "Erreur lors de l'ajout du commentaire",
                Error = ex.Message
            });
        }
    }

    /// <summary>
    /// Approuve une étape du workflow.
    /// </summary>
    [HttpPost("{workflowId}/approuver-etape")]
    public async Task<IActionResult> ApprouverEtape(Guid workflowId, [FromBody] ApprouverEtapeRequest request)
    {
        try
        {
            _logger.LogInformation($"Approbation de l'étape pour le workflow {workflowId}");
            // Garantir la présence des colonnes nécessaires (si la migration n'est pas appliquée)
            await EnsureEtapeSchemaAsync();

            var workflow = await _context.WorkflowsApprobation
                .Include(w => w.Etapes)
                .FirstOrDefaultAsync(w => w.Id == workflowId);

            if (workflow == null)
            {
                return NotFound($"Workflow {workflowId} non trouvé");
            }

            // Trouver la prochaine étape en attente
            var etapeEnAttente = workflow.Etapes
                .Where(e => e.Statut == "EN_ATTENTE")
                .OrderBy(e => e.Ordre)
                .FirstOrDefault();

            if (etapeEnAttente == null)
            {
                return BadRequest("Aucune étape en attente d'approbation");
            }

            // Autorisations: l'utilisateur doit être l'approbateur assigné OU, si non assigné, membre du rôle responsable
            if (!string.IsNullOrWhiteSpace(etapeEnAttente.RoleResponsable))
            {
                // Vérifier l'appartenance au rôle si l'étape a un rôle responsable
                bool EstMembreRole(Guid uid, string roleNom)
                {
                    var role = _context.Roles.FirstOrDefault(r => r.Nom == roleNom);
                    if (role == null) return false;
                    var direct = _context.UtilisateursRoles.Any(ur => ur.RoleId == role.Id && ur.UtilisateurId == uid);
                    if (direct) return true;
                    var viaGroupe = _context.GroupesRoles
                        .Where(gr => gr.RoleId == role.Id)
                        .Join(_context.UtilisateursGroupes, gr => gr.GroupeId, ug => ug.GroupeId, (gr, ug) => ug)
                        .Any(ug => ug.UtilisateurId == uid);
                    return viaGroupe;
                }

                if (etapeEnAttente.ApprobateurId != Guid.Empty)
                {
                    // Étape déjà assignée: seul l'approbateur assigné peut approuver et il doit encore appartenir au rôle
                    if (request.ApprobateurId != etapeEnAttente.ApprobateurId)
                    {
                        _logger.LogWarning("403 ASSIGNE_DIFFERENT approbation: workflow={WorkflowId}, etape={EtapeId}, uid={Uid}, assignedTo={AssignedTo}", workflowId, etapeEnAttente.Id, request.ApprobateurId, etapeEnAttente.ApprobateurId);
                        return StatusCode(403, new { Success = false, Code = "ASSIGNE_DIFFERENT", Message = "Cette étape est assignée à un autre approbateur.", AssignedTo = etapeEnAttente.ApprobateurId, RequiredRole = etapeEnAttente.RoleResponsable });
                    }
                    if (!EstMembreRole(request.ApprobateurId, etapeEnAttente.RoleResponsable))
                    {
                        if (AdminBypassEnabled() && EstAdministrateur(request.ApprobateurId))
                        {
                            _logger.LogWarning("Bypass administrateur approbation: workflow={WorkflowId}, etape={EtapeId}, uid={Uid}", workflowId, etapeEnAttente.Id, request.ApprobateurId);
                        }
                        else
                        {
                            _logger.LogWarning("403 ROLE_REQUIS approbation: workflow={WorkflowId}, etape={EtapeId}, uid={Uid}, roleRequis={Role}", workflowId, etapeEnAttente.Id, request.ApprobateurId, etapeEnAttente.RoleResponsable);
                            return StatusCode(403, new { Success = false, Code = "ROLE_REQUIS", Message = "Vous n’avez plus le rôle requis pour cette étape.", RequiredRole = etapeEnAttente.RoleResponsable });
                        }
                    }
                }
                else
                {
                    // Non assignée: l'utilisateur qui approuve doit appartenir au rôle responsable
                    if (!EstMembreRole(request.ApprobateurId, etapeEnAttente.RoleResponsable))
                    {
                        if (AdminBypassEnabled() && EstAdministrateur(request.ApprobateurId))
                        {
                            _logger.LogWarning("Bypass administrateur approbation (étape non assignée): workflow={WorkflowId}, etape={EtapeId}, uid={Uid}", workflowId, etapeEnAttente.Id, request.ApprobateurId);
                        }
                        else
                        {
                            _logger.LogWarning("403 ROLE_REQUIS approbation (étape non assignée): workflow={WorkflowId}, etape={EtapeId}, uid={Uid}, roleRequis={Role}", workflowId, etapeEnAttente.Id, request.ApprobateurId, etapeEnAttente.RoleResponsable);
                            return StatusCode(403, new { Success = false, Code = "ROLE_REQUIS", Message = "Vous n’avez plus le rôle requis pour cette étape.", RequiredRole = etapeEnAttente.RoleResponsable });
                        }
                    }
                    etapeEnAttente.ApprobateurId = request.ApprobateurId;
                }
            }

            // Approuver l'étape
            etapeEnAttente.Statut = "APPROUVE";
            etapeEnAttente.DateApprobation = DateTime.Now;
            etapeEnAttente.ApprobateurId = request.ApprobateurId;
            etapeEnAttente.EcheanceUtc = null; // etapa ya cerrada
            if (!string.IsNullOrWhiteSpace(request.Commentaire))
            {
                etapeEnAttente.Commentaires = request.Commentaire;
            }

            // Définir l'échéance de la prochaine étape qui devient active (SLA)
            var prochaineEtape = workflow.Etapes
                .OrderBy(e => e.Ordre)
                .FirstOrDefault(e => e.Statut == "EN_ATTENTE");
            if (prochaineEtape != null)
            {
                // Calcular SLA desde configuración (jours par défaut)
                var joursParDefaut = HttpContext.RequestServices.GetService(typeof(IConfiguration)) is IConfiguration cfg
                    ? (cfg.GetValue<int?>("Workflow:Sla:JoursParDefaut") ?? 5)
                    : 5;
                prochaineEtape.EcheanceUtc = DateTime.UtcNow.AddDays(joursParDefaut);
            }

            // Vérifier si toutes les étapes sont approuvées (après la mise à jour précédente)
            var toutesEtapesApprouvees = workflow.Etapes.All(e => e.Statut == "APPROUVE");

            // Mettre à jour le statut du workflow
            var statutId = await ObtenirIdStatutParNom(toutesEtapesApprouvees ? "APPROUVE" : "EN_COURS");
            workflow.Statut = await _context.WorkflowStatuts.FindAsync(statutId);
            workflow.StatutId = statutId;
            workflow.DateApprobationFinale = toutesEtapesApprouvees ? DateTime.Now : null;
            workflow.DateModification = DateTime.Now;
            workflow.ModifiePar = "System";

            await _context.SaveChangesAsync();

            _logger.LogInformation($"Étape {etapeEnAttente.Id} approuvée avec succès");

            return Ok(new
            {
                Success = true,
                Message = "Étape approuvée avec succès",
                WorkflowStatut = workflow.Statut,
                ToutesEtapesApprouvees = toutesEtapesApprouvees
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'approbation de l'étape");
            return StatusCode(500, new
            {
                Success = false,
                Message = "Erreur lors de l'approbation de l'étape",
                Error = ex.Message
            });
        }
    }

    /// <summary>
    /// Rejette une étape du workflow.
    /// </summary>
    [HttpPost("{workflowId}/rejeter-etape")]
    public async Task<IActionResult> RejeterEtape(Guid workflowId, [FromBody] RejeterEtapeRequest request)
    {
        try
        {
            _logger.LogInformation($"Rejet de l'étape pour le workflow {workflowId}");
            // Garantir la présence des colonnes nécessaires
            await EnsureEtapeSchemaAsync();

            if (string.IsNullOrEmpty(request.Commentaire))
            {
                return BadRequest("Un commentaire est obligatoire pour rejeter une étape");
            }

            var workflow = await _context.WorkflowsApprobation
                .Include(w => w.Etapes)
                .FirstOrDefaultAsync(w => w.Id == workflowId);

            if (workflow == null)
            {
                return NotFound($"Workflow {workflowId} non trouvé");
            }

            // Trouver la prochaine étape en attente
            var etapeEnAttente = workflow.Etapes
                .Where(e => e.Statut == "EN_ATTENTE")
                .OrderBy(e => e.Ordre)
                .FirstOrDefault();

            if (etapeEnAttente == null)
            {
                return BadRequest("Aucune étape en attente de traitement");
            }

            // Autorisations similaires à l'approbation
            if (!string.IsNullOrWhiteSpace(etapeEnAttente.RoleResponsable))
            {
                bool EstMembreRole(Guid uid, string roleNom)
                {
                    var role = _context.Roles.FirstOrDefault(r => r.Nom == roleNom);
                    if (role == null) return false;
                    var direct = _context.UtilisateursRoles.Any(ur => ur.RoleId == role.Id && ur.UtilisateurId == uid);
                    if (direct) return true;
                    var viaGroupe = _context.GroupesRoles
                        .Where(gr => gr.RoleId == role.Id)
                        .Join(_context.UtilisateursGroupes, gr => gr.GroupeId, ug => ug.GroupeId, (gr, ug) => ug)
                        .Any(ug => ug.UtilisateurId == uid);
                    return viaGroupe;
                }

                if (etapeEnAttente.ApprobateurId != Guid.Empty)
                {
                    if (request.ApprobateurId != etapeEnAttente.ApprobateurId)
                    {
                        _logger.LogWarning("403 ASSIGNE_DIFFERENT rejet: workflow={WorkflowId}, etape={EtapeId}, uid={Uid}, assignedTo={AssignedTo}", workflowId, etapeEnAttente.Id, request.ApprobateurId, etapeEnAttente.ApprobateurId);
                        return StatusCode(403, new { Success = false, Code = "ASSIGNE_DIFFERENT", Message = "Cette étape est assignée à un autre approbateur.", AssignedTo = etapeEnAttente.ApprobateurId, RequiredRole = etapeEnAttente.RoleResponsable });
                    }
                    if (!EstMembreRole(request.ApprobateurId, etapeEnAttente.RoleResponsable))
                    {
                        if (AdminBypassEnabled() && EstAdministrateur(request.ApprobateurId))
                        {
                            _logger.LogWarning("Bypass administrateur rejet: workflow={WorkflowId}, etape={EtapeId}, uid={Uid}", workflowId, etapeEnAttente.Id, request.ApprobateurId);
                        }
                        else
                        {
                            _logger.LogWarning("403 ROLE_REQUIS rejet: workflow={WorkflowId}, etape={EtapeId}, uid={Uid}, roleRequis={Role}", workflowId, etapeEnAttente.Id, request.ApprobateurId, etapeEnAttente.RoleResponsable);
                            return StatusCode(403, new { Success = false, Code = "ROLE_REQUIS", Message = "Vous n’avez plus le rôle requis pour cette étape.", RequiredRole = etapeEnAttente.RoleResponsable });
                        }
                    }
                }
                else
                {
                    if (!EstMembreRole(request.ApprobateurId, etapeEnAttente.RoleResponsable))
                    {
                        if (AdminBypassEnabled() && EstAdministrateur(request.ApprobateurId))
                        {
                            _logger.LogWarning("Bypass administrateur rejet (étape non assignée): workflow={WorkflowId}, etape={EtapeId}, uid={Uid}", workflowId, etapeEnAttente.Id, request.ApprobateurId);
                        }
                        else
                        {
                            _logger.LogWarning("403 ROLE_REQUIS rejet (étape non assignée): workflow={WorkflowId}, etape={EtapeId}, uid={Uid}, roleRequis={Role}", workflowId, etapeEnAttente.Id, request.ApprobateurId, etapeEnAttente.RoleResponsable);
                            return StatusCode(403, new { Success = false, Code = "ROLE_REQUIS", Message = "Vous n’avez plus le rôle requis pour cette étape.", RequiredRole = etapeEnAttente.RoleResponsable });
                        }
                    }
                    etapeEnAttente.ApprobateurId = request.ApprobateurId;
                }
            }

            // Rejeter l'étape
            etapeEnAttente.Statut = "REJETE";
            etapeEnAttente.DateApprobation = DateTime.Now;
            etapeEnAttente.ApprobateurId = request.ApprobateurId;
            etapeEnAttente.EcheanceUtc = null; // étape terminée (rejetée)
            etapeEnAttente.Commentaires = request.Commentaire;

            // Mettre à jour le statut du workflow à REJETE
            var statutRejete = await ObtenirIdStatutParNom("REJETE");
            workflow.Statut = await _context.WorkflowStatuts.FindAsync(statutRejete);
            workflow.StatutId = statutRejete;
            workflow.DateApprobationFinale = DateTime.Now;
            workflow.DateModification = DateTime.Now;
            workflow.ModifiePar = "System";

            // Suppression de la logique spécifique à ActifDonnees. Utiliser la nouvelle association si nécessaire.

            await _context.SaveChangesAsync();

            _logger.LogInformation($"Étape {etapeEnAttente.Id} rejetée avec succès");

            return Ok(new
            {
                Success = true,
                Message = "Étape rejetée avec succès",
                WorkflowStatut = workflow.Statut,
                CommentaireRejet = request.Commentaire
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du rejet de l'étape");
            return StatusCode(500, new
            {
                Success = false,
                Message = "Erreur lors du rejet de l'étape",
                Error = ex.Message
            });
        }
    }

    /// <summary>
    /// Annule un workflow d'approbation.
    /// </summary>
    [HttpPost("{workflowId}/annuler")]
    public async Task<IActionResult> AnnulerWorkflow(Guid workflowId, [FromBody] AnnulerWorkflowRequest request)
    {
        try
        {
            _logger.LogInformation($"Annulation du workflow {workflowId}");

            if (string.IsNullOrEmpty(request.Commentaire))
            {
                return BadRequest("Un commentaire est obligatoire pour annuler un workflow");
            }

            var workflow = await _context.WorkflowsApprobation
                .FirstOrDefaultAsync(w => w.Id == workflowId);

            if (workflow == null)
            {
                return NotFound($"Workflow {workflowId} non trouvé");
            }

            // Annuler le workflow
            workflow.Statut = await _context.WorkflowStatuts.FindAsync(StatutAnnule);
            workflow.StatutId = StatutAnnule;
            workflow.DateApprobationFinale = DateTime.Now;
            workflow.DateModification = DateTime.Now;
            workflow.ModifiePar = "System";
            // Note: CommentaireAnnulation n'existe pas dans l'entité, on pourrait l'ajouter ou utiliser une autre approche

            // Suppression de la logique spécifique à ActifDonnees. Utiliser la nouvelle association si nécessaire.

            await _context.SaveChangesAsync();

            _logger.LogInformation($"Workflow {workflowId} annulé avec succès");

            return Ok(new
            {
                Success = true,
                Message = "Workflow annulé avec succès"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'annulation du workflow");
            return StatusCode(500, new
            {
                Success = false,
                Message = "Erreur lors de l'annulation du workflow",
                Error = ex.Message
            });
        }
    }
}

/// <summary>
/// Modèle de requête pour créer un workflow.
/// </summary>
    public class CreerWorkflowRequest
    {
        [StringLength(200, ErrorMessage = "Le nom ne peut pas dépasser 200 caractères")]
        public string? Nom { get; set; }

        [StringLength(1000, ErrorMessage = "La description ne peut pas dépasser 1000 caractères")]
        public string? Description { get; set; }

        public string? Type { get; set; }

        public Guid InitiateurId { get; set; }
        public string? Commentaire { get; set; }

        // Association optionnelle à un objet métier (polymorphe)
        // Valeurs permises pour ObjetType: ActifDonnees, TermeGlossaire, Metadonnee, DomaineGouvernance, DomaineAffaires, ProduitDonnees, SchemaMetadonnees, TypeMetadonnee, CategorieMetadonnee
        public string? ObjetType { get; set; }
        public Guid? ObjetId { get; set; }
    }

/// <summary>
/// Modèle de requête pour approuver une étape.
/// </summary>
public class ApprouverEtapeRequest
{
    public Guid ApprobateurId { get; set; }
    public string? Commentaire { get; set; }
}

/// <summary>
/// Modèle de requête pour rejeter une étape.
/// </summary>
public class RejeterEtapeRequest
{
    public Guid ApprobateurId { get; set; }
    public string Commentaire { get; set; } = string.Empty;
}

/// <summary>
/// Modèle de requête pour annuler un workflow.
/// </summary>
public class AnnulerWorkflowRequest
{
    public Guid UtilisateurId { get; set; }
    public string Commentaire { get; set; } = string.Empty;
}

/// <summary>
/// Modèle de requête pour ajouter un commentaire à un workflow.
/// </summary>
public class AjouterCommentaireRequest
{
    public string Contenu { get; set; } = string.Empty;
    public string UtilisateurId { get; set; } = string.Empty;
    public string? UtilisateurNom { get; set; }
}

/// <summary>
/// Modèle de requête pour réassigner l'approbateur d'une étape.
/// </summary>
public class ReassignerEtapeRequest
{
    public Guid NouvelApprobateurId { get; set; }
    public string? Commentaire { get; set; }
}

/// <summary>
/// Modèle de requête pour "prendre" (claim) une étape par un membre du rôle responsable.
/// </summary>
public class PrendreEtapeRequest
{
    public Guid UtilisateurId { get; set; }
}
