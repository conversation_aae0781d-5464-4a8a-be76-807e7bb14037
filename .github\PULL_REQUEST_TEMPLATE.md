## Objet de la PR

- [ ] Correction
- [ ] Fonctionnalité
- [ ] Maintenance / Docs

## Description

Décrivez brièvement les changements.

## Checklist conformité (obligatoire)
- [ ] Noms de dossiers/fichiers/classes/méthodes en français canadien
- [ ] Commentaires, messages d’erreur et textes UI en français
- [ ] Accents utilisés correctement (sauf contraintes techniques)
- [ ] Conventions .NET respectées (PascalCase/camelCase/constantes)
- [ ] Routes/API en français (si nouveau endpoint)
- [ ] Pas d’anglicismes non justifiés

## Tests
- [ ] Build passe localement
- [ ] Tests existants passent
- [ ] Scénarios critiques manuels vérifiés

## Notes

Ajoutez toute considération particulière (compatibilité, migrations, etc.).
