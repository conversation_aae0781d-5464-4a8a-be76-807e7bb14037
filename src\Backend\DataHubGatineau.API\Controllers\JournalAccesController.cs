using DataHubGatineau.Application.DTOs.Identity;
using DataHubGatineau.Application.Services.Identity.Interfaces;
using DataHubGatineau.Domain.Entites.Identity;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using DataHubGatineau.Core.Constants;

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// Contrôleur pour la gestion du journal d'accès.
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize(Policy = "Role:AdministrateurSysteme")]
public class JournalAccesController : ControllerBase
{
    private readonly IServiceJournalAcces _serviceJournalAcces;
    private readonly ILogger<JournalAccesController> _logger;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="JournalAccesController"/>.
    /// </summary>
    /// <param name="serviceJournalAcces">Service de gestion du journal d'accès.</param>
    /// <param name="logger">Logger.</param>
    public JournalAccesController(IServiceJournalAcces serviceJournalAcces, ILogger<JournalAccesController> logger)
    {
        _serviceJournalAcces = serviceJournalAcces;
        _logger = logger;
    }

    /// <summary>
    /// Obtient toutes les entrées du journal d'accès.
    /// </summary>
    /// <param name="debut">Date de début (optionnel).</param>
    /// <param name="fin">Date de fin (optionnel).</param>
    /// <returns>Liste des entrées du journal d'accès.</returns>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<JournalAccesDTO>>> ObtenirToutes([FromQuery] DateTime? debut = null, [FromQuery] DateTime? fin = null)
    {
        try
        {
            var entrees = await _serviceJournalAcces.ObtenirToutesAsync(debut, fin);
            return Ok(entrees);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des entrées du journal d'accès");
            return StatusCode(500, "Une erreur est survenue lors de la récupération des entrées du journal d'accès");
        }
    }

    /// <summary>
    /// Obtient une entrée du journal d'accès par son identifiant.
    /// </summary>
    /// <param name="id">Identifiant de l'entrée du journal d'accès.</param>
    /// <returns>L'entrée du journal d'accès correspondante.</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<JournalAccesDTO>> ObtenirParId(Guid id)
    {
        try
        {
            var entree = await _serviceJournalAcces.ObtenirParIdAsync(id);
            if (entree == null)
            {
                return NotFound($"Entrée du journal d'accès avec l'ID {id} non trouvée");
            }

            return Ok(entree);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de l'entrée du journal d'accès avec l'ID {EntreeId}", id);
            return StatusCode(500, "Une erreur est survenue lors de la récupération de l'entrée du journal d'accès");
        }
    }

    /// <summary>
    /// Obtient les entrées du journal d'accès pour un utilisateur spécifique.
    /// </summary>
    /// <param name="utilisateurId">Identifiant de l'utilisateur.</param>
    /// <param name="debut">Date de début (optionnel).</param>
    /// <param name="fin">Date de fin (optionnel).</param>
    /// <returns>Liste des entrées du journal d'accès pour l'utilisateur spécifié.</returns>
    [HttpGet("utilisateur/{utilisateurId}")]
    public async Task<ActionResult<IEnumerable<JournalAccesDTO>>> ObtenirParUtilisateur(Guid utilisateurId, [FromQuery] DateTime? debut = null, [FromQuery] DateTime? fin = null)
    {
        try
        {
            var entrees = await _serviceJournalAcces.ObtenirParUtilisateurAsync(utilisateurId, debut, fin);
            return Ok(entrees);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des entrées du journal d'accès pour l'utilisateur avec l'ID {UtilisateurId}", utilisateurId);
            return StatusCode(500, "Une erreur est survenue lors de la récupération des entrées du journal d'accès");
        }
    }

    /// <summary>
    /// Obtient les entrées du journal d'accès pour un type d'action spécifique.
    /// </summary>
    /// <param name="typeAction">Type d'action.</param>
    /// <param name="debut">Date de début (optionnel).</param>
    /// <param name="fin">Date de fin (optionnel).</param>
    /// <returns>Liste des entrées du journal d'accès pour le type d'action spécifié.</returns>
    [HttpGet("type-action/{typeAction}")]
    public async Task<ActionResult<IEnumerable<JournalAccesDTO>>> ObtenirParTypeAction(TypeActionAcces typeAction, [FromQuery] DateTime? debut = null, [FromQuery] DateTime? fin = null)
    {
        try
        {
            var entrees = await _serviceJournalAcces.ObtenirParTypeActionAsync(typeAction, debut, fin);
            return Ok(entrees);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des entrées du journal d'accès pour le type d'action {TypeAction}", typeAction);
            return StatusCode(500, "Une erreur est survenue lors de la récupération des entrées du journal d'accès");
        }
    }

    /// <summary>
    /// Obtient les entrées du journal d'accès pour une ressource spécifique.
    /// </summary>
    /// <param name="ressourceId">Identifiant de la ressource.</param>
    /// <param name="typeRessource">Type de ressource.</param>
    /// <param name="debut">Date de début (optionnel).</param>
    /// <param name="fin">Date de fin (optionnel).</param>
    /// <returns>Liste des entrées du journal d'accès pour la ressource spécifiée.</returns>
    [HttpGet("ressource/{ressourceId}/{typeRessource}")]
    public async Task<ActionResult<IEnumerable<JournalAccesDTO>>> ObtenirParRessource(Guid ressourceId, string typeRessource, [FromQuery] DateTime? debut = null, [FromQuery] DateTime? fin = null)
    {
        try
        {
            var entrees = await _serviceJournalAcces.ObtenirParRessourceAsync(ressourceId, typeRessource, debut, fin);
            return Ok(entrees);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des entrées du journal d'accès pour la ressource {RessourceId} de type {TypeRessource}", ressourceId, typeRessource);
            return StatusCode(500, "Une erreur est survenue lors de la récupération des entrées du journal d'accès");
        }
    }

    /// <summary>
    /// Obtient les entrées du journal d'accès pour les accès refusés.
    /// </summary>
    /// <param name="debut">Date de début (optionnel).</param>
    /// <param name="fin">Date de fin (optionnel).</param>
    /// <returns>Liste des entrées du journal d'accès pour les accès refusés.</returns>
    [HttpGet("acces-refuses")]
    public async Task<ActionResult<IEnumerable<JournalAccesDTO>>> ObtenirAccesRefuses([FromQuery] DateTime? debut = null, [FromQuery] DateTime? fin = null)
    {
        try
        {
            var entrees = await _serviceJournalAcces.ObtenirAccesRefusesAsync(debut, fin);
            return Ok(entrees);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des entrées du journal d'accès pour les accès refusés");
            return StatusCode(500, "Une erreur est survenue lors de la récupération des entrées du journal d'accès");
        }
    }

    /// <summary>
    /// Génère un rapport d'audit pour une période donnée.
    /// </summary>
    /// <param name="debut">Date de début.</param>
    /// <param name="fin">Date de fin.</param>
    /// <returns>Rapport d'audit au format CSV.</returns>
    [HttpGet("rapport")]
    public async Task<ActionResult> GenererRapport([FromQuery] DateTime debut, [FromQuery] DateTime fin)
    {
        try
        {
            var rapport = await _serviceJournalAcces.GenererRapportAuditAsync(debut, fin);
            return File(rapport, "text/csv", $"rapport-audit-{debut:yyyy-MM-dd}-{fin:yyyy-MM-dd}.csv");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la génération du rapport d'audit pour la période du {Debut} au {Fin}", debut, fin);
            return StatusCode(500, "Une erreur est survenue lors de la génération du rapport d'audit");
        }
    }

    /// <summary>
    /// Ajoute une entrée dans le journal d'accès.
    /// </summary>
    /// <param name="utilisateurId">Identifiant de l'utilisateur.</param>
    /// <param name="typeAction">Type d'action.</param>
    /// <param name="adresseIP">Adresse IP de l'utilisateur.</param>
    /// <param name="estAutorise">Indique si l'accès a été autorisé.</param>
    /// <param name="ressourceId">Identifiant de la ressource (optionnel).</param>
    /// <param name="typeRessource">Type de ressource (optionnel).</param>
    /// <param name="agentUtilisateur">Agent utilisateur (optionnel).</param>
    /// <param name="detailsAction">Détails de l'action (optionnel).</param>
    /// <param name="raisonRefus">Raison du refus (optionnel).</param>
    /// <returns>L'entrée du journal créée.</returns>
    [HttpPost]
    [AllowAnonymous] // Cette méthode peut être appelée par le système
    public async Task<ActionResult<JournalAccesDTO>> AjouterEntree(
        [FromQuery] Guid utilisateurId,
        [FromQuery] TypeActionAcces typeAction,
        [FromQuery] string adresseIP,
        [FromQuery] bool estAutorise,
        [FromQuery] Guid? ressourceId = null,
        [FromQuery] string? typeRessource = null,
        [FromQuery] string? agentUtilisateur = null,
        [FromBody] string? detailsAction = null,
        [FromQuery] string? raisonRefus = null)
    {
        try
        {
            var entree = await _serviceJournalAcces.AjouterEntreeJournalAsync(
                utilisateurId,
                typeAction,
                adresseIP,
                estAutorise,
                ressourceId,
                typeRessource,
                agentUtilisateur,
                detailsAction,
                raisonRefus);

            return CreatedAtAction(nameof(ObtenirParId), new { id = entree.Id }, entree);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ajout d'une entrée au journal d'accès pour l'utilisateur {UtilisateurId}", utilisateurId);
            return StatusCode(500, "Une erreur est survenue lors de l'ajout d'une entrée au journal d'accès");
        }
    }
}
