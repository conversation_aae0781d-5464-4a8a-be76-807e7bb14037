using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// Contrôleur pour les sources d'actifs de données.
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class SourceActifDonneesController : ControllerBase
{
    private readonly IDepotBase<SourceActifDonnees> _depot;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="SourceActifDonneesController"/>.
    /// </summary>
    /// <param name="depot">Le dépôt des sources d'actifs de données.</param>
    public SourceActifDonneesController(IDepotBase<SourceActifDonnees> depot)
    {
        _depot = depot;
    }

    /// <summary>
    /// Obtient toutes les sources d'actifs de données.
    /// </summary>
    /// <returns>Les sources d'actifs de données.</returns>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<SourceActifDonnees>>> ObtenirTous()
    {
        var sources = await _depot.ObtenirTousAsync();
        return Ok(sources);
    }

    /// <summary>
    /// Obtient une source d'actif de données par son identifiant.
    /// </summary>
    /// <param name="id">L'identifiant de la source d'actif de données.</param>
    /// <returns>La source d'actif de données.</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<SourceActifDonnees>> ObtenirParId(Guid id)
    {
        var source = await _depot.ObtenirParIdAsync(id);
        if (source == null)
        {
            return NotFound();
        }

        return Ok(source);
    }

    /// <summary>
    /// Crée une nouvelle source d'actif de données.
    /// </summary>
    /// <param name="source">La source d'actif de données à créer.</param>
    /// <returns>La source d'actif de données créée.</returns>
    [HttpPost]
    public async Task<ActionResult<SourceActifDonnees>> Creer(SourceActifDonnees source)
    {
        await _depot.AjouterAsync(source);
        return CreatedAtAction(nameof(ObtenirParId), new { id = source.Id }, source);
    }

    /// <summary>
    /// Met à jour une source d'actif de données.
    /// </summary>
    /// <param name="id">L'identifiant de la source d'actif de données.</param>
    /// <param name="source">La source d'actif de données à mettre à jour.</param>
    /// <returns>Aucun contenu.</returns>
    [HttpPut("{id}")]
    public async Task<IActionResult> MettreAJour(Guid id, SourceActifDonnees source)
    {
        if (id != source.Id)
        {
            return BadRequest();
        }

        await _depot.MettreAJourAsync(source);
        return NoContent();
    }

    /// <summary>
    /// Supprime une source d'actif de données.
    /// </summary>
    /// <param name="id">L'identifiant de la source d'actif de données.</param>
    /// <returns>Aucun contenu.</returns>
    [HttpDelete("{id}")]
    public async Task<IActionResult> Supprimer(Guid id)
    {
        var source = await _depot.ObtenirParIdAsync(id);
        if (source == null)
        {
            return NotFound();
        }

        await _depot.SupprimerAsync(id);
        return NoContent();
    }
}
