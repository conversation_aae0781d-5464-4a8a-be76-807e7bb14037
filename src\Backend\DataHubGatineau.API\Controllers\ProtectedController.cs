using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using DataHubGatineau.Core.Constants;

namespace DataHubGatineau.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ProtectedController : ControllerBase
{
    [HttpGet("public")]
    [AllowAnonymous]
    public IActionResult GetPublicData()
    {
        return Ok(new { message = "Ceci est une donnée publique accessible à tous" });
    }

    [HttpGet("user")]
    [Authorize(Policy = "AnyRole:UtilisateurStandard,AdministrateurSysteme")]
    public IActionResult GetUserData()
    {
        return Ok(new { message = "Ceci est une donnée accessible aux utilisateurs authentifiés avec le rôle 'User' ou 'Administrator'" });
    }

    [HttpGet("admin")]
    [Authorize(Policy = "Role:AdministrateurSysteme")]
    public IActionResult GetAdminData()
    {
        return Ok(new { message = "Ceci est une donnée accessible uniquement aux administrateurs" });
    }
}
