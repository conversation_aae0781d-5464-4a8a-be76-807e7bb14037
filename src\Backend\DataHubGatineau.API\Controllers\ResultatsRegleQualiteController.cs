﻿using Asp.Versioning;
using DataHubGatineau.Application.Interfaces;
using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Enums;
using Microsoft.AspNetCore.Mvc;

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// ContrÃ´leur pour les opÃ©rations sur les rÃ©sultats de rÃ¨gles de qualitÃ©.
/// </summary>
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/ResultatsRegleQualite")]
public class ResultatsRegleQualiteController : ApiControllerBase
{
    private readonly IServiceResultatRegleQualite _serviceResultatRegleQualite;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="ResultatsRegleQualiteController"/>.
    /// </summary>
    /// <param name="serviceResultatRegleQualite">Service des rÃ©sultats de rÃ¨gles de qualitÃ©.</param>
    public ResultatsRegleQualiteController(IServiceResultatRegleQualite serviceResultatRegleQualite)
    {
        _serviceResultatRegleQualite = serviceResultatRegleQualite;
    }

    /// <summary>
    /// Obtient tous les rÃ©sultats de rÃ¨gles de qualitÃ©.
    /// </summary>
    /// <returns>Une collection de rÃ©sultats de rÃ¨gles de qualitÃ©.</returns>
    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<ResultatRegleQualite>>> ObtenirTous()
    {
        var resultats = await _serviceResultatRegleQualite.ObtenirTousAsync();
        return Ok(resultats);
    }

    /// <summary>
    /// Obtient un rÃ©sultat de rÃ¨gle de qualitÃ© par son identifiant.
    /// </summary>
    /// <param name="id">Identifiant du rÃ©sultat de rÃ¨gle de qualitÃ©.</param>
    /// <returns>Le rÃ©sultat de rÃ¨gle de qualitÃ© si trouvÃ©.</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ResultatRegleQualite>> ObtenirParId(Guid id)
    {
        var resultat = await _serviceResultatRegleQualite.ObtenirParIdAsync(id);
        if (resultat == null)
        {
            return NotFound();
        }

        return Ok(resultat);
    }

    /// <summary>
    /// Obtient les rÃ©sultats par rÃ¨gle de qualitÃ©.
    /// </summary>
    /// <param name="regleQualiteId">Identifiant de la rÃ¨gle de qualitÃ©.</param>
    /// <returns>Une collection de rÃ©sultats associÃ©s Ã  la rÃ¨gle de qualitÃ© spÃ©cifiÃ©e.</returns>
    [HttpGet("regle/{regleQualiteId}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<ResultatRegleQualite>>> ObtenirParRegleQualite(Guid regleQualiteId)
    {
        var resultats = await _serviceResultatRegleQualite.ObtenirParRegleQualiteAsync(regleQualiteId);
        return Ok(resultats);
    }

    /// <summary>
    /// Obtient les rÃ©sultats par statut.
    /// </summary>
    /// <param name="statut">Statut du rÃ©sultat.</param>
    /// <returns>Une collection de rÃ©sultats avec le statut spÃ©cifiÃ©.</returns>
    [HttpGet("statut/{statut}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<ResultatRegleQualite>>> ObtenirParStatut(StatutResultatRegleQualite statut)
    {
        var resultats = await _serviceResultatRegleQualite.ObtenirParStatutAsync(statut);
        return Ok(resultats);
    }

    /// <summary>
    /// Obtient les rÃ©sultats par pÃ©riode.
    /// </summary>
    /// <param name="dateDebut">Date de dÃ©but de la pÃ©riode (format: yyyy-MM-dd).</param>
    /// <param name="dateFin">Date de fin de la pÃ©riode (format: yyyy-MM-dd).</param>
    /// <returns>Une collection de rÃ©sultats exÃ©cutÃ©s pendant la pÃ©riode spÃ©cifiÃ©e.</returns>
    [HttpGet("periode")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<IEnumerable<ResultatRegleQualite>>> ObtenirParPeriode([FromQuery] DateTime dateDebut, [FromQuery] DateTime dateFin)
    {
        if (dateDebut > dateFin)
        {
            return BadRequest("La date de dÃ©but doit Ãªtre antÃ©rieure Ã  la date de fin.");
        }

        var resultats = await _serviceResultatRegleQualite.ObtenirParPeriodeAsync(dateDebut, dateFin);
        return Ok(resultats);
    }

    /// <summary>
    /// Obtient les derniers rÃ©sultats pour chaque rÃ¨gle de qualitÃ©.
    /// </summary>
    /// <returns>Une collection des derniers rÃ©sultats pour chaque rÃ¨gle de qualitÃ©.</returns>
    [HttpGet("derniers")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<ResultatRegleQualite>>> ObtenirDerniersResultats()
    {
        var resultats = await _serviceResultatRegleQualite.ObtenirDerniersResultatsAsync();
        return Ok(resultats);
    }

    /// <summary>
    /// Ajoute un nouveau rÃ©sultat de rÃ¨gle de qualitÃ©.
    /// </summary>
    /// <param name="resultat">RÃ©sultat de rÃ¨gle de qualitÃ© Ã  ajouter.</param>
    /// <returns>Le rÃ©sultat de rÃ¨gle de qualitÃ© ajoutÃ©.</returns>
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ResultatRegleQualite>> Ajouter(ResultatRegleQualite resultat)
    {
        if (resultat == null)
        {
            return BadRequest();
        }

        var resultatAjoute = await _serviceResultatRegleQualite.AjouterAsync(resultat);
        return CreatedAtAction(nameof(ObtenirParId), new { id = resultatAjoute.Id }, resultatAjoute);
    }

    /// <summary>
    /// Met Ã  jour un rÃ©sultat de rÃ¨gle de qualitÃ© existant.
    /// </summary>
    /// <param name="id">Identifiant du rÃ©sultat de rÃ¨gle de qualitÃ©.</param>
    /// <param name="resultat">RÃ©sultat de rÃ¨gle de qualitÃ© Ã  mettre Ã  jour.</param>
    /// <returns>Aucun contenu si la mise Ã  jour est rÃ©ussie.</returns>
    [HttpPut("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> MettreAJour(Guid id, ResultatRegleQualite resultat)
    {
        if (resultat == null || id != resultat.Id)
        {
            return BadRequest();
        }

        var resultatExistant = await _serviceResultatRegleQualite.ObtenirParIdAsync(id);
        if (resultatExistant == null)
        {
            return NotFound();
        }

        await _serviceResultatRegleQualite.MettreAJourAsync(resultat);
        return NoContent();
    }

    /// <summary>
    /// Supprime un rÃ©sultat de rÃ¨gle de qualitÃ©.
    /// </summary>
    /// <param name="id">Identifiant du rÃ©sultat de rÃ¨gle de qualitÃ© Ã  supprimer.</param>
    /// <returns>Aucun contenu si la suppression est rÃ©ussie.</returns>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Supprimer(Guid id)
    {
        var resultatExistant = await _serviceResultatRegleQualite.ObtenirParIdAsync(id);
        if (resultatExistant == null)
        {
            return NotFound();
        }

        await _serviceResultatRegleQualite.SupprimerAsync(id);
        return NoContent();
    }
}
