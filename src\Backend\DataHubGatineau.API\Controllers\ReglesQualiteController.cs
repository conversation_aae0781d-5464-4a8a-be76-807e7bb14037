using Asp.Versioning;
using DataHubGatineau.Application.Interfaces;
using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Enums;
using DataHubGatineau.API.Models;
using Microsoft.AspNetCore.Mvc;

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// ContrÃ´leur pour les opÃ©rations sur les rÃ¨gles de qualitÃ©.
/// </summary>
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/ReglesQualite")]
public class ReglesQualiteController : ApiControllerBase
{
    private readonly IServiceRegleQualite _serviceRegleQualite;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="ReglesQualiteController"/>.
    /// </summary>
    /// <param name="serviceRegleQualite">Service des rÃ¨gles de qualitÃ©.</param>
    public ReglesQualiteController(IServiceRegleQualite serviceRegleQualite)
    {
        _serviceRegleQualite = serviceRegleQualite;
    }

    /// <summary>
    /// Obtient toutes les rÃ¨gles de qualitÃ©.
    /// </summary>
    /// <returns>Une collection de rÃ¨gles de qualitÃ©.</returns>
    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<RegleQualite>>> ObtenirTous()
    {
        var regles = await _serviceRegleQualite.ObtenirTousAsync();
        return Ok(regles);
    }

    /// <summary>
    /// Obtient une rÃ¨gle de qualitÃ© par son identifiant.
    /// </summary>
    /// <param name="id">Identifiant de la rÃ¨gle de qualitÃ©.</param>
    /// <returns>La rÃ¨gle de qualitÃ© si trouvÃ©e.</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<RegleQualite>> ObtenirParId(Guid id)
    {
        var regle = await _serviceRegleQualite.ObtenirParIdAsync(id);
        if (regle == null)
        {
            return NotFound();
        }

        return Ok(regle);
    }

    /// <summary>
    /// Obtient les rÃ¨gles de qualitÃ© par actif de donnÃ©es.
    /// </summary>
    /// <param name="actifDonneesId">Identifiant de l'actif de donnÃ©es.</param>
    /// <returns>Une collection de rÃ¨gles de qualitÃ© associÃ©es Ã  l'actif de donnÃ©es spÃ©cifiÃ©.</returns>
    [HttpGet("actif/{actifDonneesId}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<RegleQualite>>> ObtenirParActifDonnees(Guid actifDonneesId)
    {
        var regles = await _serviceRegleQualite.ObtenirParActifDonneesAsync(actifDonneesId);
        return Ok(regles);
    }

    /// <summary>
    /// Obtient les rÃ¨gles de qualitÃ© par type.
    /// </summary>
    /// <param name="type">Type de rÃ¨gle de qualitÃ©.</param>
    /// <returns>Une collection de rÃ¨gles de qualitÃ© du type spÃ©cifiÃ©.</returns>
    [HttpGet("type/{type}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<RegleQualite>>> ObtenirParType(TypeRegleQualite type)
    {
        var regles = await _serviceRegleQualite.ObtenirParTypeAsync(type);
        return Ok(regles);
    }

    /// <summary>
    /// Obtient les rÃ¨gles de qualitÃ© par sÃ©vÃ©ritÃ©.
    /// </summary>
    /// <param name="severite">SÃ©vÃ©ritÃ© de la rÃ¨gle de qualitÃ©.</param>
    /// <returns>Une collection de rÃ¨gles de qualitÃ© avec la sÃ©vÃ©ritÃ© spÃ©cifiÃ©e.</returns>
    [HttpGet("severite/{severite}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<RegleQualite>>> ObtenirParSeverite(SeveriteRegleQualite severite)
    {
        var regles = await _serviceRegleQualite.ObtenirParSeveriteAsync(severite);
        return Ok(regles);
    }

    /// <summary>
    /// ExÃ©cute une rÃ¨gle de qualitÃ©.
    /// </summary>
    /// <param name="id">Identifiant de la rÃ¨gle de qualitÃ© Ã  exÃ©cuter.</param>
    /// <returns>Le rÃ©sultat de l'exÃ©cution de la rÃ¨gle de qualitÃ©.</returns>
    [HttpPost("{id}/executer")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ResultatRegleQualiteDto>> ExecuterRegle(Guid id)
    {
        var regle = await _serviceRegleQualite.ObtenirParIdAsync(id);
        if (regle == null)
        {
            return NotFound();
        }

        var resultat = await _serviceRegleQualite.ExecuterRegleAsync(id);

        // Convertir vers DTO pour éviter les références circulaires
        var dto = new ResultatRegleQualiteDto
        {
            Id = resultat.Id,
            DateExecution = resultat.DateExecution,
            Statut = resultat.Statut,
            ValeurMesuree = resultat.ValeurMesuree,
            Message = resultat.Message,
            Details = resultat.Details,
            RegleQualiteId = resultat.RegleQualiteId,
            RegleQualite = regle != null ? new RegleQualiteInfoDto
            {
                Id = regle.Id,
                Nom = regle.Nom,
                Description = regle.Description,
                Type = regle.Type,
                Seuil = regle.Seuil,
                Severite = regle.Severite,
                ActifDonneesId = regle.ActifDonneesId,
                Champ = regle.Champ,
                EstActive = regle.EstActive
            } : null
        };

        return Ok(dto);
    }

    /// <summary>
    /// Ajoute une nouvelle rÃ¨gle de qualitÃ©.
    /// </summary>
    /// <param name="regle">RÃ¨gle de qualitÃ© Ã  ajouter.</param>
    /// <returns>La rÃ¨gle de qualitÃ© ajoutÃ©e.</returns>
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<RegleQualite>> Ajouter([FromBody] RegleQualite regle)
    {
        if (regle == null)
        {
            return BadRequest();
        }

        var regleAjoutee = await _serviceRegleQualite.AjouterAsync(regle);
        return CreatedAtAction(nameof(ObtenirParId), new { id = regleAjoutee.Id }, regleAjoutee);
    }

    /// <summary>
    /// Met Ã  jour une rÃ¨gle de qualitÃ© existante.
    /// </summary>
    /// <param name="id">Identifiant de la rÃ¨gle de qualitÃ©.</param>
    /// <param name="regle">RÃ¨gle de qualitÃ© Ã  mettre Ã  jour.</param>
    /// <returns>La rÃ¨gle de qualitÃ© mise Ã  jour.</returns>
    [HttpPut("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<RegleQualite>> MettreAJour(Guid id, [FromBody] RegleQualite regle)
    {
        if (regle == null || id != regle.Id)
        {
            return BadRequest();
        }

        var regleExistant = await _serviceRegleQualite.ObtenirParIdAsync(id);
        if (regleExistant == null)
        {
            return NotFound();
        }

        await _serviceRegleQualite.MettreAJourAsync(regle);
        return Ok(regle);
    }

    /// <summary>
    /// Supprime une rÃ¨gle de qualitÃ©.
    /// </summary>
    /// <param name="id">Identifiant de la rÃ¨gle de qualitÃ© Ã  supprimer.</param>
    /// <returns>Aucun contenu si la suppression est rÃ©ussie.</returns>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Supprimer(Guid id)
    {
        var regleExistante = await _serviceRegleQualite.ObtenirParIdAsync(id);
        if (regleExistante == null)
        {
            return NotFound();
        }

        await _serviceRegleQualite.SupprimerAsync(id);
        return NoContent();
    }
}
