using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// Contrôleur pour les fréquences de mise à jour.
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class FrequenceMiseAJourController : ControllerBase
{
    private readonly IDepotBase<FrequenceMiseAJour> _depot;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="FrequenceMiseAJourController"/>.
    /// </summary>
    /// <param name="depot">Le dépôt des fréquences de mise à jour.</param>
    public FrequenceMiseAJourController(IDepotBase<FrequenceMiseAJour> depot)
    {
        _depot = depot;
    }

    /// <summary>
    /// Obtient toutes les fréquences de mise à jour.
    /// </summary>
    /// <returns>Les fréquences de mise à jour.</returns>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<FrequenceMiseAJour>>> ObtenirTous()
    {
        var frequences = await _depot.ObtenirTousAsync();
        return Ok(frequences);
    }

    /// <summary>
    /// Obtient une fréquence de mise à jour par son identifiant.
    /// </summary>
    /// <param name="id">L'identifiant de la fréquence de mise à jour.</param>
    /// <returns>La fréquence de mise à jour.</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<FrequenceMiseAJour>> ObtenirParId(Guid id)
    {
        var frequence = await _depot.ObtenirParIdAsync(id);
        if (frequence == null)
        {
            return NotFound();
        }

        return Ok(frequence);
    }

    /// <summary>
    /// Crée une nouvelle fréquence de mise à jour.
    /// </summary>
    /// <param name="frequence">La fréquence de mise à jour à créer.</param>
    /// <returns>La fréquence de mise à jour créée.</returns>
    [HttpPost]
    public async Task<ActionResult<FrequenceMiseAJour>> Creer(FrequenceMiseAJour frequence)
    {
        await _depot.AjouterAsync(frequence);
        return CreatedAtAction(nameof(ObtenirParId), new { id = frequence.Id }, frequence);
    }

    /// <summary>
    /// Met à jour une fréquence de mise à jour.
    /// </summary>
    /// <param name="id">L'identifiant de la fréquence de mise à jour.</param>
    /// <param name="frequence">La fréquence de mise à jour à mettre à jour.</param>
    /// <returns>Aucun contenu.</returns>
    [HttpPut("{id}")]
    public async Task<IActionResult> MettreAJour(Guid id, FrequenceMiseAJour frequence)
    {
        if (id != frequence.Id)
        {
            return BadRequest();
        }

        await _depot.MettreAJourAsync(frequence);
        return NoContent();
    }

    /// <summary>
    /// Supprime une fréquence de mise à jour.
    /// </summary>
    /// <param name="id">L'identifiant de la fréquence de mise à jour.</param>
    /// <returns>Aucun contenu.</returns>
    [HttpDelete("{id}")]
    public async Task<IActionResult> Supprimer(Guid id)
    {
        var frequence = await _depot.ObtenirParIdAsync(id);
        if (frequence == null)
        {
            return NotFound();
        }

        await _depot.SupprimerAsync(id);
        return NoContent();
    }
}
