﻿using Asp.Versioning;
using DataHubGatineau.Application.Interfaces;
using DataHubGatineau.Domain.Entites;
using Microsoft.AspNetCore.Mvc;

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// ContrÃ´leur pour les opÃ©rations sur les mÃ©tadonnÃ©es.
/// </summary>
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/Metadonnees")]
public class MetadonneesController : ApiControllerBase
{
    private readonly ILogger<MetadonneesController> _logger;
    private readonly IServiceMetadonnee _serviceMetadonnee;
    private readonly IServiceEvaluateurExpressionsDynamique _serviceEvaluateur;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="MetadonneesController"/>.
    /// </summary>
    /// <param name="serviceMetadonnee">Service des mÃ©tadonnÃ©es.</param>
    /// <param name="serviceEvaluateur">Service d'évaluation d'expressions dynamiques.</param>
    /// <param name="logger">Logger.</param>
    public MetadonneesController(IServiceMetadonnee serviceMetadonnee, IServiceEvaluateurExpressionsDynamique serviceEvaluateur, ILogger<MetadonneesController> logger)
    {
        _serviceMetadonnee = serviceMetadonnee;
        _serviceEvaluateur = serviceEvaluateur;
        _logger = logger;
    }

    /// <summary>
    /// Obtient toutes les mÃ©tadonnÃ©es.
    /// </summary>
    /// <returns>Une collection de mÃ©tadonnÃ©es.</returns>
    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<Metadonnee>>> ObtenirTous()
    {
        var metadonnees = await _serviceMetadonnee.ObtenirTousAsync();
        return Ok(metadonnees);
    }

    /// <summary>
    /// Obtient une mÃ©tadonnÃ©e par son identifiant.
    /// </summary>
    /// <param name="id">Identifiant de la mÃ©tadonnÃ©e.</param>
    /// <returns>La mÃ©tadonnÃ©e si trouvÃ©e.</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Metadonnee>> ObtenirParId(Guid id)
    {
        var metadonnee = await _serviceMetadonnee.ObtenirParIdAsync(id);
        if (metadonnee == null)
        {
            return NotFound();
        }

        return Ok(metadonnee);
    }

    /// <summary>
    /// Obtient les mÃ©tadonnÃ©es par actif de donnÃ©es.
    /// </summary>
    /// <param name="actifDonneesId">Identifiant de l'actif de donnÃ©es.</param>
    /// <returns>Une collection de mÃ©tadonnÃ©es associÃ©es Ã  l'actif de donnÃ©es spÃ©cifiÃ©.</returns>
    [HttpGet("actif/{actifDonneesId}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<Metadonnee>>> ObtenirParActifDonnees(Guid actifDonneesId)
    {
        var metadonnees = await _serviceMetadonnee.ObtenirParActifDonneesAsync(actifDonneesId);
        return Ok(metadonnees);
    }

    /// <summary>
    /// Obtient les mÃ©tadonnÃ©es par type.
    /// </summary>
    /// <param name="typeId">Identifiant du type de mÃ©tadonnÃ©e.</param>
    /// <returns>Une collection de mÃ©tadonnÃ©es du type spÃ©cifiÃ©.</returns>
    [HttpGet("type/{typeId}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<Metadonnee>>> ObtenirParType(Guid typeId)
    {
        var metadonnees = await _serviceMetadonnee.ObtenirParTypeAsync(typeId);
        return Ok(metadonnees);
    }

    /// <summary>
    /// Obtient les mÃ©tadonnÃ©es par nom.
    /// </summary>
    /// <param name="nom">Nom de la mÃ©tadonnÃ©e.</param>
    /// <returns>Une collection de mÃ©tadonnÃ©es avec le nom spÃ©cifiÃ©.</returns>
    [HttpGet("nom/{nom}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<Metadonnee>>> ObtenirParNom(string nom)
    {
        var metadonnees = await _serviceMetadonnee.ObtenirParNomAsync(nom);
        return Ok(metadonnees);
    }

    /// <summary>
    /// Ajoute une nouvelle mÃ©tadonnÃ©e.
    /// </summary>
    /// <param name="metadonnee">MÃ©tadonnÃ©e Ã  ajouter.</param>
    /// <returns>La mÃ©tadonnÃ©e ajoutÃ©e.</returns>
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<Metadonnee>> Ajouter(Metadonnee metadonnee)
    {
        try
        {
            _logger.LogInformation("Tentative d'ajout d'une mÃ©tadonnÃ©e: {MetadonneeJson}", System.Text.Json.JsonSerializer.Serialize(metadonnee));

            if (metadonnee == null)
            {
                _logger.LogWarning("La mÃ©tadonnÃ©e est null");
                return BadRequest("La mÃ©tadonnÃ©e ne peut pas Ãªtre null");
            }

            // VÃ©rifier les propriÃ©tÃ©s obligatoires
            if (string.IsNullOrEmpty(metadonnee.Nom))
            {
                _logger.LogWarning("Le nom de la mÃ©tadonnÃ©e est vide");
                return BadRequest("Le nom de la mÃ©tadonnÃ©e est obligatoire");
            }

            if (string.IsNullOrEmpty(metadonnee.Valeur))
            {
                _logger.LogWarning("La valeur de la mÃ©tadonnÃ©e est vide");
                return BadRequest("La valeur de la mÃ©tadonnÃ©e est obligatoire");
            }

            if (metadonnee.ActifDonneesId == Guid.Empty)
            {
                _logger.LogWarning("L'ID de l'actif de donnÃ©es est vide");
                return BadRequest("L'ID de l'actif de donnÃ©es est obligatoire");
            }

            // Valider que TypeId est défini
            if (metadonnee.TypeId == Guid.Empty)
            {
                _logger.LogWarning("TypeId est vide pour la métadonnée: {Nom}", metadonnee.Nom);
                return BadRequest("Le type de métadonnée est obligatoire.");
            }

            var metadonneeAjoutee = await _serviceMetadonnee.AjouterAsync(metadonnee);
            _logger.LogInformation("MÃ©tadonnÃ©e ajoutÃ©e avec succÃ¨s: {Id}", metadonneeAjoutee.Id);

            return CreatedAtAction(nameof(ObtenirParId), new { id = metadonneeAjoutee.Id }, metadonneeAjoutee);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ajout d'une mÃ©tadonnÃ©e");
            return StatusCode(StatusCodes.Status500InternalServerError, $"Une erreur est survenue lors de l'ajout de la mÃ©tadonnÃ©e: {ex.Message}");
        }
    }

    /// <summary>
    /// Met Ã  jour une mÃ©tadonnÃ©e existante.
    /// </summary>
    /// <param name="id">Identifiant de la mÃ©tadonnÃ©e.</param>
    /// <param name="metadonnee">MÃ©tadonnÃ©e Ã  mettre Ã  jour.</param>
    /// <returns>Aucun contenu si la mise Ã  jour est rÃ©ussie.</returns>
    [HttpPut("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> MettreAJour(Guid id, Metadonnee metadonnee)
    {
        if (metadonnee == null || id != metadonnee.Id)
        {
            return BadRequest();
        }

        var metadonneeExistante = await _serviceMetadonnee.ObtenirParIdAsync(id);
        if (metadonneeExistante == null)
        {
            return NotFound();
        }

        await _serviceMetadonnee.MettreAJourAsync(metadonnee);
        return NoContent();
    }

    /// <summary>
    /// Supprime une mÃ©tadonnÃ©e.
    /// </summary>
    /// <param name="id">Identifiant de la mÃ©tadonnÃ©e Ã  supprimer.</param>
    /// <returns>Aucun contenu si la suppression est rÃ©ussie.</returns>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Supprimer(Guid id)
    {
        var metadonneeExistante = await _serviceMetadonnee.ObtenirParIdAsync(id);
        if (metadonneeExistante == null)
        {
            return NotFound();
        }

        await _serviceMetadonnee.SupprimerAsync(id);
        return NoContent();
    }

    /// <summary>
    /// Calcule les métadonnées avec des formules pour un actif de données et un schéma donnés.
    /// </summary>
    /// <param name="actifDonneesId">Identifiant de l'actif de données.</param>
    /// <param name="schemaMetadonneesId">Identifiant du schéma de métadonnées.</param>
    /// <returns>Résultat du calcul avec le nombre de métadonnées mises à jour.</returns>
    [HttpPost("calculer/{actifDonneesId}/{schemaMetadonneesId}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<object>> CalculerMetadonnees(Guid actifDonneesId, Guid schemaMetadonneesId)
    {
        try
        {
            if (actifDonneesId == Guid.Empty)
            {
                return BadRequest("L'identifiant de l'actif de données est requis.");
            }

            if (schemaMetadonneesId == Guid.Empty)
            {
                return BadRequest("L'identifiant du schéma de métadonnées est requis.");
            }

            var nombreMisesAJour = await _serviceMetadonnee.CalculerMetadonneesAsync(actifDonneesId, schemaMetadonneesId);

            return Ok(new
            {
                Success = true,
                Message = $"Calcul terminé avec succès",
                NombreMetadonneesCalculees = nombreMisesAJour,
                ActifDonneesId = actifDonneesId,
                SchemaMetadonneesId = schemaMetadonneesId
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du calcul des métadonnées pour l'actif {ActifId} et schéma {SchemaId}", actifDonneesId, schemaMetadonneesId);
            return StatusCode(StatusCodes.Status500InternalServerError, $"Erreur lors du calcul des métadonnées: {ex.Message}");
        }
    }

    /// <summary>
    /// Endpoint de test pour le moteur d'évaluation d'expressions dynamiques.
    /// </summary>
    /// <param name="request">Requête contenant l'expression à évaluer.</param>
    /// <returns>Résultat de l'évaluation.</returns>
    [HttpPost("test-dynamique")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<object>> TestEvaluateurDynamique([FromBody] TestExpressionRequest request)
    {
        try
        {
            _logger.LogInformation("Request reçu: {@Request}", request);
            _logger.LogInformation("Expression reçue: {Expression}", request?.Expression);

            if (string.IsNullOrWhiteSpace(request?.Expression))
            {
                _logger.LogWarning("Expression vide ou nulle");
                return BadRequest("L'expression est requise.");
            }

            _logger.LogInformation("Évaluation de l'expression: {Expression}", request.Expression);
            var resultado = await _serviceEvaluateur.EvaluerExpressionAsync(request.Expression);

            if (resultado.Success)
            {
                _logger.LogInformation("Évaluation réussie. Résultat: {Resultat}", resultado.Result);
                return Ok(new
                {
                    Expression = request.Expression,
                    Resultat = resultado.Result,
                    Tipo = resultado.Result?.GetType().Name ?? "null",
                    Success = true
                });
            }
            else
            {
                return BadRequest(new
                {
                    Expression = request.Expression,
                    Error = resultado.ErrorMessage,
                    Success = false
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'évaluation de l'expression: {Expression}", request?.Expression);
            return StatusCode(StatusCodes.Status500InternalServerError, new
            {
                Expression = request?.Expression,
                Error = ex.Message,
                Success = false
            });
        }
    }

    /// <summary>
    /// Évalue une expression avec le contexte d'un actif de données spécifique.
    /// </summary>
    /// <param name="request">Requête contenant l'expression et l'ID de l'actif.</param>
    /// <returns>Résultat de l'évaluation avec contexte.</returns>
    [HttpPost("evaluer-expression-actif")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<object>> EvaluerExpressionAvecContexteActif([FromBody] ExpressionActifRequest request)
    {
        try
        {
            _logger.LogInformation("Évaluation d'expression avec contexte - ActifId: {ActifId}, Expression: {Expression}", 
                request?.ActifId, request?.Expression);

            if (string.IsNullOrWhiteSpace(request?.Expression))
            {
                return BadRequest("L'expression est requise.");
            }

            if (request.ActifId == Guid.Empty)
            {
                return BadRequest("L'ID de l'actif est requis.");
            }

            // Utiliser le service qui a accès au contexte de l'actif
            var resultat = await _serviceMetadonnee.EvaluerExpressionAvecContexteActifAsync(
                request.Expression, request.ActifId);

            return Ok(new
            {
                Expression = request.Expression,
                ActifId = request.ActifId,
                Resultat = resultat,
                Success = true
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'évaluation de l'expression avec contexte: {Expression}, ActifId: {ActifId}", 
                request?.Expression, request?.ActifId);
            return BadRequest(new
            {
                Expression = request?.Expression,
                ActifId = request?.ActifId,
                Error = ex.Message,
                Success = false
            });
        }
    }
}

/// <summary>
/// Requête pour tester l'évaluateur d'expressions.
/// </summary>
public class TestExpressionRequest
{
    /// <summary>
    /// Expression à évaluer.
    /// </summary>
    public string Expression { get; set; } = string.Empty;
}

/// <summary>
/// Requête pour évaluer une expression avec le contexte d'un actif.
/// </summary>
public class ExpressionActifRequest
{
    /// <summary>
    /// Expression à évaluer.
    /// </summary>
    public string Expression { get; set; } = string.Empty;

    /// <summary>
    /// ID de l'actif de données pour le contexte.
    /// </summary>
    public Guid ActifId { get; set; }
}
