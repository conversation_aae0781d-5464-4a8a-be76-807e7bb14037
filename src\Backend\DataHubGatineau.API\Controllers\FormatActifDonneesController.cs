using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// Contrôleur pour les formats d'actifs de données.
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class FormatActifDonneesController : ControllerBase
{
    private readonly IDepotBase<FormatActifDonnees> _depot;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="FormatActifDonneesController"/>.
    /// </summary>
    /// <param name="depot">Le dépôt des formats d'actifs de données.</param>
    public FormatActifDonneesController(IDepotBase<FormatActifDonnees> depot)
    {
        _depot = depot;
    }

    /// <summary>
    /// Obtient tous les formats d'actifs de données.
    /// </summary>
    /// <returns>Les formats d'actifs de données.</returns>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<FormatActifDonnees>>> ObtenirTous()
    {
        var formats = await _depot.ObtenirTousAsync();
        return Ok(formats);
    }

    /// <summary>
    /// Obtient un format d'actif de données par son identifiant.
    /// </summary>
    /// <param name="id">L'identifiant du format d'actif de données.</param>
    /// <returns>Le format d'actif de données.</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<FormatActifDonnees>> ObtenirParId(Guid id)
    {
        var format = await _depot.ObtenirParIdAsync(id);
        if (format == null)
        {
            return NotFound();
        }

        return Ok(format);
    }

    /// <summary>
    /// Crée un nouveau format d'actif de données.
    /// </summary>
    /// <param name="format">Le format d'actif de données à créer.</param>
    /// <returns>Le format d'actif de données créé.</returns>
    [HttpPost]
    public async Task<ActionResult<FormatActifDonnees>> Creer(FormatActifDonnees format)
    {
        await _depot.AjouterAsync(format);
        return CreatedAtAction(nameof(ObtenirParId), new { id = format.Id }, format);
    }

    /// <summary>
    /// Met à jour un format d'actif de données.
    /// </summary>
    /// <param name="id">L'identifiant du format d'actif de données.</param>
    /// <param name="format">Le format d'actif de données à mettre à jour.</param>
    /// <returns>Aucun contenu.</returns>
    [HttpPut("{id}")]
    public async Task<IActionResult> MettreAJour(Guid id, FormatActifDonnees format)
    {
        if (id != format.Id)
        {
            return BadRequest();
        }

        await _depot.MettreAJourAsync(format);
        return NoContent();
    }

    /// <summary>
    /// Supprime un format d'actif de données.
    /// </summary>
    /// <param name="id">L'identifiant du format d'actif de données.</param>
    /// <returns>Aucun contenu.</returns>
    [HttpDelete("{id}")]
    public async Task<IActionResult> Supprimer(Guid id)
    {
        var format = await _depot.ObtenirParIdAsync(id);
        if (format == null)
        {
            return NotFound();
        }

        await _depot.SupprimerAsync(id);
        return NoContent();
    }
}
