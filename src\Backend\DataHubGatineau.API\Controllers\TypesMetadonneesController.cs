﻿using Asp.Versioning;
using DataHubGatineau.Application.Interfaces;
using DataHubGatineau.Domain.Entites;
using Microsoft.AspNetCore.Mvc;

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// ContrÃ´leur pour les opÃ©rations sur les types de mÃ©tadonnÃ©es.
/// </summary>
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/TypesMetadonnees")]
public class TypesMetadonneesController : ApiControllerBase
{
    private readonly IServiceTypeMetadonnee _serviceTypeMetadonnee;
    private readonly ILogger<TypesMetadonneesController> _logger;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="TypesMetadonneesController"/>.
    /// </summary>
    /// <param name="serviceTypeMetadonnee">Service des types de mÃ©tadonnÃ©es.</param>
    /// <param name="logger">Logger.</param>
    public TypesMetadonneesController(IServiceTypeMetadonnee serviceTypeMetadonnee, ILogger<TypesMetadonneesController> logger)
    {
        _serviceTypeMetadonnee = serviceTypeMetadonnee;
        _logger = logger;
    }

    /// <summary>
    /// Obtient tous les types de mÃ©tadonnÃ©es.
    /// </summary>
    /// <returns>Une collection de types de mÃ©tadonnÃ©es.</returns>
    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TypeMetadonnee>>> ObtenirTous()
    {
        try
        {
            var typesMetadonnees = await _serviceTypeMetadonnee.ObtenirTousAsync();
            return Ok(typesMetadonnees);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention de tous les types de mÃ©tadonnÃ©es");
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de l'obtention des types de mÃ©tadonnÃ©es");
        }
    }

    /// <summary>
    /// Obtient un type de mÃ©tadonnÃ©e par son identifiant.
    /// </summary>
    /// <param name="id">Identifiant du type de mÃ©tadonnÃ©e.</param>
    /// <returns>Le type de mÃ©tadonnÃ©e si trouvÃ©.</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<TypeMetadonnee>> ObtenirParId(Guid id)
    {
        try
        {
            var typeMetadonnee = await _serviceTypeMetadonnee.ObtenirParIdAsync(id);
            if (typeMetadonnee == null)
            {
                return NotFound();
            }

            return Ok(typeMetadonnee);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention du type de mÃ©tadonnÃ©e avec l'ID {TypeMetadonneeId}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de l'obtention du type de mÃ©tadonnÃ©e");
        }
    }

    /// <summary>
    /// Obtient un type de mÃ©tadonnÃ©e par son nom.
    /// </summary>
    /// <param name="nom">Nom du type de mÃ©tadonnÃ©e.</param>
    /// <returns>Le type de mÃ©tadonnÃ©e si trouvÃ©.</returns>
    [HttpGet("nom/{nom}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<TypeMetadonnee>> ObtenirParNom(string nom)
    {
        try
        {
            var typeMetadonnee = await _serviceTypeMetadonnee.ObtenirParNomAsync(nom);
            if (typeMetadonnee == null)
            {
                return NotFound();
            }

            return Ok(typeMetadonnee);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention du type de mÃ©tadonnÃ©e avec le nom {TypeMetadonneeNom}", nom);
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de l'obtention du type de mÃ©tadonnÃ©e");
        }
    }

    /// <summary>
    /// CrÃ©e un nouveau type de mÃ©tadonnÃ©e.
    /// </summary>
    /// <param name="typeMetadonnee">Type de mÃ©tadonnÃ©e Ã  crÃ©er.</param>
    /// <returns>Le type de mÃ©tadonnÃ©e crÃ©Ã©.</returns>
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<TypeMetadonnee>> Creer(TypeMetadonnee typeMetadonnee)
    {
        try
        {
            if (typeMetadonnee == null)
            {
                return BadRequest();
            }

            var typeMetadonneeAjoute = await _serviceTypeMetadonnee.CreerAsync(typeMetadonnee);
            return CreatedAtAction(nameof(ObtenirParId), new { id = typeMetadonneeAjoute.Id }, typeMetadonneeAjoute);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la crÃ©ation d'un type de mÃ©tadonnÃ©e");
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de la crÃ©ation du type de mÃ©tadonnÃ©e");
        }
    }

    /// <summary>
    /// Met Ã  jour un type de mÃ©tadonnÃ©e existant.
    /// </summary>
    /// <param name="id">Identifiant du type de mÃ©tadonnÃ©e.</param>
    /// <param name="typeMetadonnee">Type de mÃ©tadonnÃ©e Ã  mettre Ã  jour.</param>
    /// <returns>Aucun contenu si la mise Ã  jour est rÃ©ussie.</returns>
    [HttpPut("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> MettreAJour(Guid id, TypeMetadonnee typeMetadonnee)
    {
        try
        {
            if (typeMetadonnee == null || id != typeMetadonnee.Id)
            {
                return BadRequest();
            }

            var typeMetadonneeExistant = await _serviceTypeMetadonnee.ObtenirParIdAsync(id);
            if (typeMetadonneeExistant == null)
            {
                return NotFound();
            }

            await _serviceTypeMetadonnee.MettreAJourAsync(typeMetadonnee);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise Ã  jour du type de mÃ©tadonnÃ©e avec l'ID {TypeMetadonneeId}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de la mise Ã  jour du type de mÃ©tadonnÃ©e");
        }
    }

    /// <summary>
    /// Supprime un type de mÃ©tadonnÃ©e.
    /// </summary>
    /// <param name="id">Identifiant du type de mÃ©tadonnÃ©e Ã  supprimer.</param>
    /// <returns>Aucun contenu si la suppression est rÃ©ussie.</returns>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Supprimer(Guid id)
    {
        try
        {
            var typeMetadonneeExistant = await _serviceTypeMetadonnee.ObtenirParIdAsync(id);
            if (typeMetadonneeExistant == null)
            {
                return NotFound();
            }

            await _serviceTypeMetadonnee.SupprimerAsync(id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression du type de mÃ©tadonnÃ©e avec l'ID {TypeMetadonneeId}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de la suppression du type de mÃ©tadonnÃ©e");
        }
    }
}
