using DataHubGatineau.Domain.Entites.Workflow;
using DataHubGatineau.API.Services;
using DataHubGatineau.Infrastructure.Persistence;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Asp.Versioning;

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// Contrôleur pour l'accès aux statuts de workflow.
/// </summary>
[ApiController]
// Exponer rutas tanto con versión fija v2 como con segmento de versión para compatibilidad
[ApiVersion("2.0")]
[Route("api/v2/WorkflowStatuts")]
[Route("api/v{version:apiVersion}/WorkflowStatuts")]
public class WorkflowStatutsController : ControllerBase
{
    private readonly CentreDonneesDbContext _context;
    private readonly WorkflowInitializationService _initService;

    public WorkflowStatutsController(CentreDonneesDbContext context, WorkflowInitializationService initService)
    {
        _context = context;
        _initService = initService;
    }

    /// <summary>
    /// Retourne la liste des statuts de workflow actifs.
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<WorkflowStatut>>> GetStatuts()
    {
        var statuts = await _context.Set<WorkflowStatut>()
            .Where(s => s.Actif)
            .OrderBy(s => s.Ordre)
            .ToListAsync();
        return Ok(statuts);
    }

    /// <summary>
    /// Normalise et déduplique les statuts de workflow (maintenance).
    /// </summary>
    [HttpPost("normaliser")]
    public async Task<IActionResult> Normaliser()
    {
        await _initService.NormaliserStatutsWorkflowAsync();
        return Ok(new { Success = true });
    }
} 