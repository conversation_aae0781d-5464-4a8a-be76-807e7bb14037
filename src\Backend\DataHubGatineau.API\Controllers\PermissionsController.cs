using DataHubGatineau.Application.DTOs.Identity;
using DataHubGatineau.Application.Services.Identity.Interfaces;
using DataHubGatineau.Domain.Entites.Identity;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
// using DataHubGatineau.Core.Constants; // Remplacé par des stratégies d'autorisation dynamiques basées BD

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// Contrôleur pour la gestion des permissions.
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize(Policy = "Role:AdministrateurSysteme")]
public class PermissionsController : ControllerBase
{
    private readonly IServicePermission _servicePermission;
    private readonly ILogger<PermissionsController> _logger;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="PermissionsController"/>.
    /// </summary>
    /// <param name="servicePermission">Service de gestion des permissions.</param>
    /// <param name="logger">Logger.</param>
    public PermissionsController(IServicePermission servicePermission, ILogger<PermissionsController> logger)
    {
        _servicePermission = servicePermission;
        _logger = logger;
    }

    /// <summary>
    /// Obtient toutes les permissions.
    /// </summary>
    /// <returns>Liste des permissions.</returns>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<PermissionDTO>>> ObtenirToutes()
    {
        try
        {
            var permissions = await _servicePermission.ObtenirToutesAsync();
            return Ok(permissions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de toutes les permissions");
            return StatusCode(500, "Une erreur est survenue lors de la récupération des permissions");
        }
    }

    /// <summary>
    /// Obtient une permission par son identifiant.
    /// </summary>
    /// <param name="id">Identifiant de la permission.</param>
    /// <returns>La permission correspondante.</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<PermissionDTO>> ObtenirParId(Guid id)
    {
        try
        {
            var permission = await _servicePermission.ObtenirParIdAsync(id);
            if (permission == null)
            {
                return NotFound($"Permission avec l'ID {id} non trouvée");
            }

            return Ok(permission);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de la permission avec l'ID {PermissionId}", id);
            return StatusCode(500, "Une erreur est survenue lors de la récupération de la permission");
        }
    }

    /// <summary>
    /// Obtient une permission par son code.
    /// </summary>
    /// <param name="code">Code de la permission.</param>
    /// <returns>La permission correspondante.</returns>
    [HttpGet("code/{code}")]
    public async Task<ActionResult<PermissionDTO>> ObtenirParCode(string code)
    {
        try
        {
            var permission = await _servicePermission.ObtenirParCodeAsync(code);
            if (permission == null)
            {
                return NotFound($"Permission avec le code '{code}' non trouvée");
            }

            return Ok(permission);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de la permission avec le code {CodePermission}", code);
            return StatusCode(500, "Une erreur est survenue lors de la récupération de la permission");
        }
    }

    /// <summary>
    /// Obtient les permissions par catégorie.
    /// </summary>
    /// <param name="categorie">Catégorie de permissions.</param>
    /// <returns>Liste des permissions de la catégorie spécifiée.</returns>
    [HttpGet("categorie/{categorie}")]
    public async Task<ActionResult<IEnumerable<PermissionDTO>>> ObtenirParCategorie(string categorie)
    {
        try
        {
            var permissions = await _servicePermission.ObtenirParCategorieAsync(categorie);
            return Ok(permissions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des permissions de la catégorie {Categorie}", categorie);
            return StatusCode(500, "Une erreur est survenue lors de la récupération des permissions");
        }
    }

    /// <summary>
    /// Obtient les permissions par type.
    /// </summary>
    /// <param name="type">Type de permission.</param>
    /// <returns>Liste des permissions du type spécifié.</returns>
    [HttpGet("type/{type}")]
    public async Task<ActionResult<IEnumerable<PermissionDTO>>> ObtenirParType(TypePermission type)
    {
        try
        {
            var permissions = await _servicePermission.ObtenirParTypeAsync(type);
            return Ok(permissions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des permissions de type {TypePermission}", type);
            return StatusCode(500, "Une erreur est survenue lors de la récupération des permissions");
        }
    }

    /// <summary>
    /// Obtient les permissions par type de licence.
    /// </summary>
    /// <param name="typeLicence">Type de licence.</param>
    /// <returns>Liste des permissions du type de licence spécifié.</returns>
    [HttpGet("licence/{typeLicence}")]
    public async Task<ActionResult<IEnumerable<PermissionDTO>>> ObtenirParTypeLicence(TypeLicence typeLicence)
    {
        try
        {
            var permissions = await _servicePermission.ObtenirParTypeLicenceAsync(typeLicence);
            return Ok(permissions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des permissions du type de licence {TypeLicence}", typeLicence);
            return StatusCode(500, "Une erreur est survenue lors de la récupération des permissions");
        }
    }

    /// <summary>
    /// Crée une nouvelle permission.
    /// </summary>
    /// <param name="permission">Données de la permission à créer.</param>
    /// <returns>La permission créée.</returns>
    [HttpPost]
    public async Task<ActionResult<PermissionDTO>> Creer([FromBody] PermissionCreationDTO permission)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var nouvellePermission = await _servicePermission.CreerAsync(permission);
            return CreatedAtAction(nameof(ObtenirParId), new { id = nouvellePermission.Id }, nouvellePermission);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Erreur de validation lors de la création de la permission");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de la permission");
            return StatusCode(500, "Une erreur est survenue lors de la création de la permission");
        }
    }

    /// <summary>
    /// Met à jour une permission existante.
    /// </summary>
    /// <param name="id">Identifiant de la permission.</param>
    /// <param name="permission">Données de la permission à mettre à jour.</param>
    /// <returns>La permission mise à jour.</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<PermissionDTO>> MettreAJour(Guid id, [FromBody] PermissionMiseAJourDTO permission)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var permissionMiseAJour = await _servicePermission.MettreAJourAsync(id, permission);
            return Ok(permissionMiseAJour);
        }
        catch (KeyNotFoundException ex)
        {
            _logger.LogWarning(ex, "Permission avec l'ID {PermissionId} non trouvée", id);
            return NotFound(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Erreur de validation lors de la mise à jour de la permission avec l'ID {PermissionId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour de la permission avec l'ID {PermissionId}", id);
            return StatusCode(500, "Une erreur est survenue lors de la mise à jour de la permission");
        }
    }

    /// <summary>
    /// Supprime une permission.
    /// </summary>
    /// <param name="id">Identifiant de la permission.</param>
    /// <returns>Résultat de la suppression.</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult> Supprimer(Guid id)
    {
        try
        {
            var resultat = await _servicePermission.SupprimerAsync(id);
            if (!resultat)
            {
                return NotFound($"Permission avec l'ID {id} non trouvée");
            }

            return NoContent();
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Erreur de validation lors de la suppression de la permission avec l'ID {PermissionId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de la permission avec l'ID {PermissionId}", id);
            return StatusCode(500, "Une erreur est survenue lors de la suppression de la permission");
        }
    }

    /// <summary>
    /// Obtient les rôles ayant une permission spécifique.
    /// </summary>
    /// <param name="id">Identifiant de la permission.</param>
    /// <returns>Liste des rôles ayant la permission spécifiée.</returns>
    [HttpGet("{id}/roles")]
    public async Task<ActionResult<IEnumerable<RoleDTO>>> ObtenirRoles(Guid id)
    {
        try
        {
            var roles = await _servicePermission.ObtenirRolesParPermissionAsync(id);
            return Ok(roles);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des rôles pour la permission avec l'ID {PermissionId}", id);
            return StatusCode(500, "Une erreur est survenue lors de la récupération des rôles");
        }
    }
}
