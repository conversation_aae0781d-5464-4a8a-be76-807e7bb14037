using DataHubGatineau.Application.DTOs.Identity;
using DataHubGatineau.Application.Services.Identity.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
// using DataHubGatineau.Core.Constants; // Remplacé par des stratégies d'autorisation dynamiques basées BD

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// Contrôleur pour la gestion des groupes.
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize(Policy = "Role:AdministrateurSysteme")]
public class GroupesController : ControllerBase
{
    private readonly IServiceGroupe _serviceGroupe;
    private readonly ILogger<GroupesController> _logger;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="GroupesController"/>.
    /// </summary>
    /// <param name="serviceGroupe">Service de gestion des groupes.</param>
    /// <param name="logger">Logger.</param>
    public GroupesController(IServiceGroupe serviceGroupe, ILogger<GroupesController> logger)
    {
        _serviceGroupe = serviceGroupe;
        _logger = logger;
    }

    /// <summary>
    /// Obtient tous les groupes.
    /// </summary>
    /// <returns>Liste des groupes.</returns>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<GroupeDTO>>> ObtenirTous()
    {
        try
        {
            var groupes = await _serviceGroupe.ObtenirTousAsync();
            return Ok(groupes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de tous les groupes");
            return StatusCode(500, "Une erreur est survenue lors de la récupération des groupes");
        }
    }

    /// <summary>
    /// Obtient un groupe par son identifiant.
    /// </summary>
    /// <param name="id">Identifiant du groupe.</param>
    /// <returns>Le groupe correspondant.</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<GroupeDTO>> ObtenirParId(Guid id)
    {
        try
        {
            var groupe = await _serviceGroupe.ObtenirParIdAsync(id);
            if (groupe == null)
            {
                return NotFound($"Groupe avec l'ID {id} non trouvé");
            }

            return Ok(groupe);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération du groupe avec l'ID {GroupeId}", id);
            return StatusCode(500, "Une erreur est survenue lors de la récupération du groupe");
        }
    }

    /// <summary>
    /// Obtient un groupe par son nom.
    /// </summary>
    /// <param name="nom">Nom du groupe.</param>
    /// <returns>Le groupe correspondant.</returns>
    [HttpGet("nom/{nom}")]
    public async Task<ActionResult<GroupeDTO>> ObtenirParNom(string nom)
    {
        try
        {
            var groupe = await _serviceGroupe.ObtenirParNomAsync(nom);
            if (groupe == null)
            {
                return NotFound($"Groupe avec le nom '{nom}' non trouvé");
            }

            return Ok(groupe);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération du groupe avec le nom {NomGroupe}", nom);
            return StatusCode(500, "Une erreur est survenue lors de la récupération du groupe");
        }
    }

    /// <summary>
    /// Crée un nouveau groupe.
    /// </summary>
    /// <param name="groupe">Données du groupe à créer.</param>
    /// <returns>Le groupe créé.</returns>
    [HttpPost]
    public async Task<ActionResult<GroupeDTO>> Creer([FromBody] GroupeCreationDTO groupe)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var nouveauGroupe = await _serviceGroupe.CreerAsync(groupe);
            return CreatedAtAction(nameof(ObtenirParId), new { id = nouveauGroupe.Id }, nouveauGroupe);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Erreur de validation lors de la création du groupe");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création du groupe");
            return StatusCode(500, "Une erreur est survenue lors de la création du groupe");
        }
    }

    /// <summary>
    /// Met à jour un groupe existant.
    /// </summary>
    /// <param name="id">Identifiant du groupe.</param>
    /// <param name="groupe">Données du groupe à mettre à jour.</param>
    /// <returns>Le groupe mis à jour.</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<GroupeDTO>> MettreAJour(Guid id, [FromBody] GroupeMiseAJourDTO groupe)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var groupeMisAJour = await _serviceGroupe.MettreAJourAsync(id, groupe);
            return Ok(groupeMisAJour);
        }
        catch (KeyNotFoundException ex)
        {
            _logger.LogWarning(ex, "Groupe avec l'ID {GroupeId} non trouvé", id);
            return NotFound(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Erreur de validation lors de la mise à jour du groupe avec l'ID {GroupeId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour du groupe avec l'ID {GroupeId}", id);
            return StatusCode(500, "Une erreur est survenue lors de la mise à jour du groupe");
        }
    }

    /// <summary>
    /// Supprime un groupe.
    /// </summary>
    /// <param name="id">Identifiant du groupe.</param>
    /// <returns>Résultat de la suppression.</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult> Supprimer(Guid id)
    {
        try
        {
            var resultat = await _serviceGroupe.SupprimerAsync(id);
            if (!resultat)
            {
                return NotFound($"Groupe avec l'ID {id} non trouvé");
            }

            return NoContent();
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Erreur de validation lors de la suppression du groupe avec l'ID {GroupeId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression du groupe avec l'ID {GroupeId}", id);
            return StatusCode(500, "Une erreur est survenue lors de la suppression du groupe");
        }
    }

    /// <summary>
    /// Obtient les utilisateurs d'un groupe.
    /// </summary>
    /// <param name="id">Identifiant du groupe.</param>
    /// <returns>Liste des utilisateurs du groupe.</returns>
    [HttpGet("{id}/utilisateurs")]
    public async Task<ActionResult<IEnumerable<UtilisateurDTO>>> ObtenirUtilisateurs(Guid id)
    {
        try
        {
            var utilisateurs = await _serviceGroupe.ObtenirUtilisateursGroupeAsync(id);
            return Ok(utilisateurs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des utilisateurs pour le groupe avec l'ID {GroupeId}", id);
            return StatusCode(500, "Une erreur est survenue lors de la récupération des utilisateurs");
        }
    }

    /// <summary>
    /// Obtient les rôles d'un groupe.
    /// </summary>
    /// <param name="id">Identifiant du groupe.</param>
    /// <returns>Liste des rôles du groupe.</returns>
    [HttpGet("{id}/roles")]
    public async Task<ActionResult<IEnumerable<RoleDTO>>> ObtenirRoles(Guid id)
    {
        try
        {
            var roles = await _serviceGroupe.ObtenirRolesGroupeAsync(id);
            return Ok(roles);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des rôles pour le groupe avec l'ID {GroupeId}", id);
            return StatusCode(500, "Une erreur est survenue lors de la récupération des rôles");
        }
    }

    /// <summary>
    /// Ajoute un utilisateur à un groupe.
    /// </summary>
    /// <param name="id">Identifiant du groupe.</param>
    /// <param name="utilisateurId">Identifiant de l'utilisateur.</param>
    /// <returns>Résultat de l'ajout.</returns>
    [HttpPost("{id}/utilisateurs/{utilisateurId}")]
    public async Task<ActionResult> AjouterUtilisateur(Guid id, Guid utilisateurId)
    {
        try
        {
            var resultat = await _serviceGroupe.AjouterUtilisateurAsync(id, utilisateurId);
            if (!resultat)
            {
                return BadRequest("Impossible d'ajouter l'utilisateur au groupe");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ajout de l'utilisateur {UtilisateurId} au groupe {GroupeId}", utilisateurId, id);
            return StatusCode(500, "Une erreur est survenue lors de l'ajout de l'utilisateur au groupe");
        }
    }

    /// <summary>
    /// Supprime un utilisateur d'un groupe.
    /// </summary>
    /// <param name="id">Identifiant du groupe.</param>
    /// <param name="utilisateurId">Identifiant de l'utilisateur.</param>
    /// <returns>Résultat de la suppression.</returns>
    [HttpDelete("{id}/utilisateurs/{utilisateurId}")]
    public async Task<ActionResult> SupprimerUtilisateur(Guid id, Guid utilisateurId)
    {
        try
        {
            var resultat = await _serviceGroupe.SupprimerUtilisateurAsync(id, utilisateurId);
            if (!resultat)
            {
                return BadRequest("Impossible de supprimer l'utilisateur du groupe");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de l'utilisateur {UtilisateurId} du groupe {GroupeId}", utilisateurId, id);
            return StatusCode(500, "Une erreur est survenue lors de la suppression de l'utilisateur du groupe");
        }
    }

    /// <summary>
    /// Ajoute un rôle à un groupe.
    /// </summary>
    /// <param name="id">Identifiant du groupe.</param>
    /// <param name="roleId">Identifiant du rôle.</param>
    /// <returns>Résultat de l'ajout.</returns>
    [HttpPost("{id}/roles/{roleId}")]
    public async Task<ActionResult> AjouterRole(Guid id, Guid roleId)
    {
        try
        {
            var resultat = await _serviceGroupe.AjouterRoleAsync(id, roleId);
            if (!resultat)
            {
                return BadRequest("Impossible d'ajouter le rôle au groupe");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ajout du rôle {RoleId} au groupe {GroupeId}", roleId, id);
            return StatusCode(500, "Une erreur est survenue lors de l'ajout du rôle au groupe");
        }
    }

    /// <summary>
    /// Supprime un rôle d'un groupe.
    /// </summary>
    /// <param name="id">Identifiant du groupe.</param>
    /// <param name="roleId">Identifiant du rôle.</param>
    /// <returns>Résultat de la suppression.</returns>
    [HttpDelete("{id}/roles/{roleId}")]
    public async Task<ActionResult> SupprimerRole(Guid id, Guid roleId)
    {
        try
        {
            var resultat = await _serviceGroupe.SupprimerRoleAsync(id, roleId);
            if (!resultat)
            {
                return BadRequest("Impossible de supprimer le rôle du groupe");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression du rôle {RoleId} du groupe {GroupeId}", roleId, id);
            return StatusCode(500, "Une erreur est survenue lors de la suppression du rôle du groupe");
        }
    }

    /// <summary>
    /// Synchronise les groupes avec un fournisseur d'identité externe.
    /// </summary>
    /// <param name="fournisseurIdentite">Fournisseur d'identité.</param>
    /// <returns>Nombre de groupes synchronisés.</returns>
    [HttpPost("synchroniser/{fournisseurIdentite}")]
    public async Task<ActionResult<int>> SynchroniserGroupesExternes(string fournisseurIdentite)
    {
        try
        {
            var nombreGroupesSynchronises = await _serviceGroupe.SynchroniserGroupesExternesAsync(fournisseurIdentite);
            return Ok(nombreGroupesSynchronises);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la synchronisation des groupes externes avec le fournisseur {FournisseurIdentite}", fournisseurIdentite);
            return StatusCode(500, "Une erreur est survenue lors de la synchronisation des groupes externes");
        }
    }
}
