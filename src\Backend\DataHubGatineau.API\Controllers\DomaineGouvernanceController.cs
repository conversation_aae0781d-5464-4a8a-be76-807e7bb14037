using DataHubGatineau.Application.DTOs;
using DataHubGatineau.Application.Interfaces;
using DataHubGatineau.Domain.Entites;
using Microsoft.AspNetCore.Mvc;

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// Contrôleur pour les opérations sur les domaines de gouvernance.
/// </summary>
[Route("api/domaines-gouvernance")]
[ApiController]
public class DomaineGouvernanceController : ControllerBase
{
    private readonly IServiceDomaineGouvernance _serviceDomaineGouvernance;
    private readonly ILogger<DomaineGouvernanceController> _logger;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="DomaineGouvernanceController"/>.
    /// </summary>
    /// <param name="serviceDomaineGouvernance">Service des domaines de gouvernance.</param>
    /// <param name="logger">Logger.</param>
    public DomaineGouvernanceController(IServiceDomaineGouvernance serviceDomaineGouvernance, ILogger<DomaineGouvernanceController> logger)
    {
        _serviceDomaineGouvernance = serviceDomaineGouvernance;
        _logger = logger;
    }

    /// <summary>
    /// Obtient tous les domaines de gouvernance.
    /// </summary>
    /// <returns>Une collection de domaines de gouvernance.</returns>
    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<DomaineGouvernanceDTO>>> ObtenirTous()
    {
        try
        {
            // Obtener directamente de la base de datos para evitar errores con columnas que no existen
            var domaines = await _serviceDomaineGouvernance.ObtenirTousAsync();

            // Mapear manualmente sin incluir las relaciones
            var domainesDTO = domaines.Select(d => new DomaineGouvernanceDTO
            {
                Id = d.Id,
                Nom = d.Nom,
                Description = d.Description,
                Proprietaire = d.Proprietaire,
                DateCreation = d.DateCreation,
                DateModification = d.DateModification,
                CreePar = d.CreePar,
                ModifiePar = d.ModifiePar,
                // No incluimos las relaciones que no existen en la base de datos
                ActifsDonneesIds = new List<Guid>(),
                TermesGlossaireIds = new List<Guid>()
            });

            return Ok(domainesDTO);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention de tous les domaines de gouvernance");
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de l'obtention des domaines de gouvernance");
        }
    }

    /// <summary>
    /// Obtient un domaine de gouvernance par son identifiant.
    /// </summary>
    /// <param name="id">Identifiant du domaine de gouvernance.</param>
    /// <returns>Le domaine de gouvernance correspondant à l'identifiant.</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<DomaineGouvernanceDTO>> ObtenirParId(Guid id)
    {
        try
        {
            var domaine = await _serviceDomaineGouvernance.ObtenirParIdAsync(id);
            if (domaine == null)
            {
                return NotFound($"Le domaine de gouvernance avec l'ID {id} n'a pas été trouvé");
            }

            // Mapear manualmente sin incluir las relaciones
            var domaineDTO = new DomaineGouvernanceDTO
            {
                Id = domaine.Id,
                Nom = domaine.Nom,
                Description = domaine.Description,
                Proprietaire = domaine.Proprietaire,
                DateCreation = domaine.DateCreation,
                DateModification = domaine.DateModification,
                CreePar = domaine.CreePar,
                ModifiePar = domaine.ModifiePar,
                // No incluimos las relaciones que no existen en la base de datos
                ActifsDonneesIds = new List<Guid>(),
                TermesGlossaireIds = new List<Guid>()
            };

            return Ok(domaineDTO);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention du domaine de gouvernance avec l'ID {Id}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de l'obtention du domaine de gouvernance");
        }
    }

    /// <summary>
    /// Obtient les domaines de gouvernance par propriétaire.
    /// </summary>
    /// <param name="proprietaire">Propriétaire des domaines de gouvernance.</param>
    /// <returns>Une collection de domaines de gouvernance.</returns>
    [HttpGet("proprietaire/{proprietaire}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<DomaineGouvernanceDTO>>> ObtenirParProprietaire(string proprietaire)
    {
        try
        {
            var domaines = await _serviceDomaineGouvernance.ObtenirParProprietaireAsync(proprietaire);
            var domainesDTO = domaines.Select(MapToDTO);
            return Ok(domainesDTO);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention des domaines de gouvernance pour le propriétaire {Proprietaire}", proprietaire);
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de l'obtention des domaines de gouvernance");
        }
    }

    /// <summary>
    /// Recherche des domaines de gouvernance par mot-clé.
    /// </summary>
    /// <param name="motCle">Mot-clé à rechercher.</param>
    /// <returns>Une collection de domaines de gouvernance.</returns>
    [HttpGet("recherche/{motCle}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<DomaineGouvernanceDTO>>> Rechercher(string motCle)
    {
        try
        {
            var domaines = await _serviceDomaineGouvernance.RechercherAsync(motCle);
            var domainesDTO = domaines.Select(MapToDTO);
            return Ok(domainesDTO);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche de domaines de gouvernance avec le mot-clé {MotCle}", motCle);
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de la recherche de domaines de gouvernance");
        }
    }

    /// <summary>
    /// Ajoute un domaine de gouvernance.
    /// </summary>
    /// <param name="domaineDTO">Domaine de gouvernance à ajouter.</param>
    /// <returns>Le domaine de gouvernance ajouté.</returns>
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<DomaineGouvernanceDTO>> Ajouter(DomaineGouvernanceDTO domaineDTO)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var domaine = MapFromDTO(domaineDTO);
            var domaineAjoute = await _serviceDomaineGouvernance.AjouterAsync(domaine);
            var domaineAjouteDTO = MapToDTO(domaineAjoute);

            return CreatedAtAction(nameof(ObtenirParId), new { id = domaineAjouteDTO.Id }, domaineAjouteDTO);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ajout d'un domaine de gouvernance");
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de l'ajout du domaine de gouvernance");
        }
    }

    /// <summary>
    /// Met à jour un domaine de gouvernance.
    /// </summary>
    /// <param name="id">Identifiant du domaine de gouvernance.</param>
    /// <param name="domaineDTO">Domaine de gouvernance à mettre à jour.</param>
    /// <returns>Le domaine de gouvernance mis à jour.</returns>
    [HttpPut("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<DomaineGouvernanceDTO>> MettreAJour(Guid id, DomaineGouvernanceDTO domaineDTO)
    {
        try
        {
            if (id != domaineDTO.Id)
            {
                return BadRequest("L'ID dans l'URL ne correspond pas à l'ID dans le corps de la requête");
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var domaineExistant = await _serviceDomaineGouvernance.ObtenirParIdAsync(id);
            if (domaineExistant == null)
            {
                return NotFound($"Le domaine de gouvernance avec l'ID {id} n'a pas été trouvé");
            }

            var domaine = MapFromDTO(domaineDTO);
            var domaineMisAJour = await _serviceDomaineGouvernance.MettreAJourAsync(domaine);
            var domaineMisAJourDTO = MapToDTO(domaineMisAJour);

            return Ok(domaineMisAJourDTO);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour du domaine de gouvernance avec l'ID {Id}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de la mise à jour du domaine de gouvernance");
        }
    }

    /// <summary>
    /// Supprime un domaine de gouvernance.
    /// </summary>
    /// <param name="id">Identifiant du domaine de gouvernance à supprimer.</param>
    /// <returns>Aucun contenu.</returns>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult> Supprimer(Guid id)
    {
        try
        {
            var domaineExistant = await _serviceDomaineGouvernance.ObtenirParIdAsync(id);
            if (domaineExistant == null)
            {
                return NotFound($"Le domaine de gouvernance avec l'ID {id} n'a pas été trouvé");
            }

            var resultat = await _serviceDomaineGouvernance.SupprimerAsync(id);
            if (!resultat)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, "La suppression du domaine de gouvernance a échoué");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression du domaine de gouvernance avec l'ID {Id}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de la suppression du domaine de gouvernance");
        }
    }

    private static DomaineGouvernanceDTO MapToDTO(DomaineGouvernance domaine)
    {
        return new DomaineGouvernanceDTO
        {
            Id = domaine.Id,
            Nom = domaine.Nom,
            Description = domaine.Description,
            Proprietaire = domaine.Proprietaire,
            // No incluimos las relaciones que no existen en la base de datos
            ActifsDonneesIds = new List<Guid>(),
            TermesGlossaireIds = new List<Guid>(),
            DateCreation = domaine.DateCreation,
            DateModification = domaine.DateModification,
            CreePar = domaine.CreePar,
            ModifiePar = domaine.ModifiePar
        };
    }

    private static DomaineGouvernance MapFromDTO(DomaineGouvernanceDTO domaineDTO)
    {
        return new DomaineGouvernance
        {
            Id = domaineDTO.Id,
            Nom = domaineDTO.Nom,
            Description = domaineDTO.Description,
            Proprietaire = domaineDTO.Proprietaire,
            DateCreation = domaineDTO.DateCreation,
            DateModification = domaineDTO.DateModification,
            CreePar = domaineDTO.CreePar,
            ModifiePar = domaineDTO.ModifiePar
        };
    }
}
