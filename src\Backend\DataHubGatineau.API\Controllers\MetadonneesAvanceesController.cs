﻿using Asp.Versioning;
using DataHubGatineau.Application.DTOs;
using DataHubGatineau.Application.Interfaces;
using DataHubGatineau.Domain.Entites;
using Microsoft.AspNetCore.Mvc;
using DataHubGatineau.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using DataHubGatineau.API.Models;

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// Contrôleur pour les opérations sur les métadonnées avancées.
/// </summary>
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/MetadonneesAvancees")]
public class MetadonneesAvanceesController : ApiControllerBase
{
    private readonly IServiceMetadonnee _serviceMetadonnee;
    private readonly IServiceTypeMetadonnee _serviceTypeMetadonnee;
    private readonly IServiceCategorieMetadonnee _serviceCategorieMetadonnee;
    private readonly ILogger<MetadonneesAvanceesController> _logger;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="MetadonneesAvanceesController"/>.
    /// </summary>
    /// <param name="serviceMetadonnee">Service des métadonnées.</param>
    /// <param name="serviceTypeMetadonnee">Service des types de métadonnées.</param>
    /// <param name="serviceCategorieMetadonnee">Service des catégories de métadonnées.</param>
    /// <param name="logger">Logger.</param>
    public MetadonneesAvanceesController(
        IServiceMetadonnee serviceMetadonnee,
        IServiceTypeMetadonnee serviceTypeMetadonnee,
        IServiceCategorieMetadonnee serviceCategorieMetadonnee,
        ILogger<MetadonneesAvanceesController> logger)
    {
        _serviceMetadonnee = serviceMetadonnee;
        _serviceTypeMetadonnee = serviceTypeMetadonnee;
        _serviceCategorieMetadonnee = serviceCategorieMetadonnee;
        _logger = logger;
    }

    /// <summary>
    /// Obtient toutes les métadonnées avancées avec pagination.
    /// </summary>
    /// <param name="page">Numéro de page (1-based)</param>
    /// <param name="pageSize">Taille de la page</param>
    /// <returns>Une page de métadonnées avancées et le total.</returns>
    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<PagedResult<MetadonneeAvanceeDTO>>> ObtenirTous([FromQuery] int page = 1, [FromQuery] int pageSize = 20)
    {
        try
        {
            _logger.LogInformation("Obtention paginée des métadonnées avancées (page {Page}, pageSize {PageSize})", page, pageSize);
            var metadonnees = await _serviceMetadonnee.ObtenirTousAsync();

            // Convertir les entités en DTOs
            var metadonneesDTOs = new List<MetadonneeAvanceeDTO>();

            foreach (var m in metadonnees)
            {
                string typeNom = "Technique";
                string categorieName = "Schema";

                if (m.TypeId.HasValue && m.TypeId.Value != Guid.Empty)
                {
                    var typeMetadonnee = await _serviceTypeMetadonnee.ObtenirParIdAsync(m.TypeId.Value);
                    if (typeMetadonnee != null)
                    {
                        typeNom = typeMetadonnee.Nom;
                    }
                }
                if (m.CategorieId.HasValue)
                {
                    var categorieMetadonnee = await _serviceCategorieMetadonnee.ObtenirParIdAsync(m.CategorieId.Value);
                    if (categorieMetadonnee != null)
                    {
                        categorieName = categorieMetadonnee.Nom;
                    }
                }
                metadonneesDTOs.Add(new MetadonneeAvanceeDTO
                {
                    Id = m.Id,
                    Nom = m.Nom,
                    Valeur = m.Valeur,
                    TypeNom = typeNom,
                    CategorieName = categorieName,
                    ActifDonneesId = m.ActifDonneesId,
                    DateCreation = m.DateCreation,
                    DateModification = m.DateModification,
                    CreePar = m.CreePar,
                    ModifiePar = m.ModifiePar
                });
            }

            var total = metadonneesDTOs.Count;
            var pageItems = metadonneesDTOs.Skip((page - 1) * pageSize).Take(pageSize).ToList();

            var result = new PagedResult<MetadonneeAvanceeDTO>
            {
                Items = pageItems,
                TotalCount = total,
                Page = page,
                PageSize = pageSize
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention paginée des métadonnées avancées");
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de l'obtention des métadonnées avancées");
        }
    }

    /// <summary>
    /// Obtient une métadonnée avancée par son identifiant.
    /// </summary>
    /// <param name="id">Identifiant de la métadonnée avancée.</param>
    /// <returns>La métadonnée avancée si trouvée.</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<MetadonneeAvanceeDTO>> ObtenirParId(Guid id)
    {
        try
        {
            _logger.LogInformation("Obtention de la métadonnée avancée avec l'ID {Id}", id);
            var metadonnee = await _serviceMetadonnee.ObtenirParIdAsync(id);
            if (metadonnee == null)
            {
                _logger.LogWarning("Métadonnée avancée avec l'ID {Id} non trouvée", id);
                return NotFound();
            }

            // Convertir l'entité en DTO
            string typeNom = "Technique";
            string categorieName = "Schema";

            // Obtenir le type de métadonnée si disponible
            if (metadonnee.TypeId.HasValue && metadonnee.TypeId.Value != Guid.Empty)
            {
                var typeMetadonnee = await _serviceTypeMetadonnee.ObtenirParIdAsync(metadonnee.TypeId.Value);
                if (typeMetadonnee != null)
                {
                    typeNom = typeMetadonnee.Nom;
                }
            }

            // Obtenir la catégorie de métadonnée si disponible
            if (metadonnee.CategorieId.HasValue)
            {
                var categorieMetadonnee = await _serviceCategorieMetadonnee.ObtenirParIdAsync(metadonnee.CategorieId.Value);
                if (categorieMetadonnee != null)
                {
                    categorieName = categorieMetadonnee.Nom;
                }
            }

            var metadonneeDTO = new MetadonneeAvanceeDTO
            {
                Id = metadonnee.Id,
                Nom = metadonnee.Nom,
                Valeur = metadonnee.Valeur,
                TypeNom = typeNom,
                CategorieName = categorieName,
                ActifDonneesId = metadonnee.ActifDonneesId,
                DateCreation = metadonnee.DateCreation,
                DateModification = metadonnee.DateModification,
                CreePar = metadonnee.CreePar,
                ModifiePar = metadonnee.ModifiePar
            };

            return Ok(metadonneeDTO);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention de la métadonnée avancée avec l'ID {Id}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, $"Une erreur est survenue lors de l'obtention de la métadonnée avancée: {ex.Message}");
        }
    }

    /// <summary>
    /// Obtient les métadonnées avancées par actif de données.
    /// </summary>
    /// <param name="actifDonneesId">Identifiant de l'actif de données.</param>
    /// <returns>Une collection de métadonnées avancées associées à l'actif de données spécifié.</returns>
    [HttpGet("actif/{actifDonneesId}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<MetadonneeAvanceeDTO>>> ObtenirParActifDonnees(Guid actifDonneesId)
    {
        try
        {
            _logger.LogInformation("Obtention des métadonnées avancées pour l'actif de données avec l'ID {ActifDonneesId}", actifDonneesId);
            var metadonnees = await _serviceMetadonnee.ObtenirParActifDonneesAsync(actifDonneesId);

            // Convertir les entités en DTOs
            var metadonneesDTOs = new List<MetadonneeAvanceeDTO>();

            foreach (var m in metadonnees)
            {
                string typeNom = "Technique";
                string categorieName = "Schema";

                // Obtenir le type de métadonnée si disponible
                if (m.TypeId.HasValue && m.TypeId.Value != Guid.Empty)
                {
                    var typeMetadonnee = await _serviceTypeMetadonnee.ObtenirParIdAsync(m.TypeId.Value);
                    if (typeMetadonnee != null)
                    {
                        typeNom = typeMetadonnee.Nom;
                    }
                }

                // Obtenir la catégorie de métadonnée si disponible
                if (m.CategorieId.HasValue)
                {
                    var categorieMetadonnee = await _serviceCategorieMetadonnee.ObtenirParIdAsync(m.CategorieId.Value);
                    if (categorieMetadonnee != null)
                    {
                        categorieName = categorieMetadonnee.Nom;
                    }
                }

                metadonneesDTOs.Add(new MetadonneeAvanceeDTO
                {
                    Id = m.Id,
                    Nom = m.Nom,
                    Valeur = m.Valeur,
                    TypeNom = typeNom,
                    CategorieName = categorieName,
                    ActifDonneesId = m.ActifDonneesId,
                    DateCreation = m.DateCreation,
                    DateModification = m.DateModification,
                    CreePar = m.CreePar,
                    ModifiePar = m.ModifiePar
                });
            }

            return Ok(metadonneesDTOs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention des métadonnées avancées pour l'actif de données avec l'ID {ActifDonneesId}", actifDonneesId);
            return StatusCode(StatusCodes.Status500InternalServerError, $"Une erreur est survenue lors de l'obtention des métadonnées avancées: {ex.Message}");
        }
    }

    /// <summary>
    /// Ajoute une nouvelle métadonnée avancée.
    /// </summary>
    /// <param name="metadonneeDTO">Métadonnée avancée à ajouter.</param>
    /// <returns>La métadonnée avancée ajoutée.</returns>
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<Metadonnee>> Ajouter(MetadonneeAvanceeDTO metadonneeDTO)
    {
        try
        {
            if (metadonneeDTO == null)
            {
                _logger.LogWarning("Tentative d'ajout d'une métadonnée avancée null");
                return BadRequest("La métadonnée avancée ne peut pas être null");
            }

            _logger.LogInformation("Ajout d'une nouvelle métadonnée avancée: {Nom}, Type: {Type}, Catégorie: {Categorie}",
                metadonneeDTO.Nom, metadonneeDTO.TypeNom, metadonneeDTO.CategorieName);

            // Convertir le DTO en entité Metadonnee
            var metadonnee = new Metadonnee
            {
                Id = metadonneeDTO.Id,
                Nom = metadonneeDTO.Nom,
                Valeur = metadonneeDTO.Valeur,
                ActifDonneesId = metadonneeDTO.ActifDonneesId,
                DateCreation = metadonneeDTO.DateCreation,
                DateModification = metadonneeDTO.DateModification,
                CreePar = metadonneeDTO.CreePar ?? "Système",
                ModifiePar = metadonneeDTO.ModifiePar ?? "Système"
            };

            // Obtenir le type de métadonnée par son nom
            var typeMetadonnee = await _serviceTypeMetadonnee.ObtenirParNomAsync(metadonneeDTO.TypeNom);
            if (typeMetadonnee != null)
            {
                metadonnee.TypeId = typeMetadonnee.Id;
            }
            else
            {
                _logger.LogWarning("Type de métadonnée '{TypeNom}' non trouvé", metadonneeDTO.TypeNom);
                // Utiliser un type par défaut si le type n'est pas trouvé
                var typeParDefaut = await _serviceTypeMetadonnee.ObtenirParNomAsync("Technique");
                if (typeParDefaut != null)
                {
                    metadonnee.TypeId = typeParDefaut.Id;
                }
            }

            // Obtenir la catégorie de métadonnée par son nom
            if (!string.IsNullOrEmpty(metadonneeDTO.CategorieName))
            {
                var categories = await _serviceCategorieMetadonnee.ObtenirTousAsync();
                var categorieMetadonnee = categories.FirstOrDefault(c => c.Nom == metadonneeDTO.CategorieName);
                if (categorieMetadonnee != null)
                {
                    metadonnee.CategorieId = categorieMetadonnee.Id;
                }
            }

            var metadonneeAjoutee = await _serviceMetadonnee.AjouterAsync(metadonnee);
            return CreatedAtAction(nameof(ObtenirParId), new { id = metadonneeAjoutee.Id }, metadonneeAjoutee);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ajout d'une métadonnée avancée");
            return StatusCode(StatusCodes.Status500InternalServerError, $"Une erreur est survenue lors de l'ajout de la métadonnée avancée: {ex.Message}");
        }
    }

    /// <summary>
    /// Met à jour une métadonnée avancée existante.
    /// </summary>
    /// <param name="id">Identifiant de la métadonnée avancée.</param>
    /// <param name="metadonneeDTO">Métadonnée avancée à mettre à jour.</param>
    /// <returns>Aucun contenu si la mise à jour est réussie.</returns>
    [HttpPut("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> MettreAJour(Guid id, MetadonneeAvanceeDTO metadonneeDTO)
    {
        try
        {
            if (metadonneeDTO == null || id != metadonneeDTO.Id)
            {
                _logger.LogWarning("Tentative de mise à jour d'une métadonnée avancée avec des données invalides. ID: {Id}, Métadonnée ID: {MetadonneeId}", id, metadonneeDTO?.Id);
                return BadRequest("Les données de la métadonnée avancée sont invalides");
            }

            var metadonneeExistante = await _serviceMetadonnee.ObtenirParIdAsync(id);
            if (metadonneeExistante == null)
            {
                _logger.LogWarning("Métadonnée avancée avec l'ID {Id} non trouvée lors de la mise à jour", id);
                return NotFound($"Métadonnée avancée avec l'ID {id} non trouvée");
            }

            // Mettre à jour les propriétés de la métadonnée existante
            metadonneeExistante.Nom = metadonneeDTO.Nom;
            metadonneeExistante.Valeur = metadonneeDTO.Valeur;
            metadonneeExistante.ActifDonneesId = metadonneeDTO.ActifDonneesId;
            metadonneeExistante.DateModification = DateTime.Now;
            metadonneeExistante.ModifiePar = metadonneeDTO.ModifiePar ?? "Système";

            // Obtenir le type de métadonnée par son nom
            var typeMetadonnee = await _serviceTypeMetadonnee.ObtenirParNomAsync(metadonneeDTO.TypeNom);
            if (typeMetadonnee != null)
            {
                metadonneeExistante.TypeId = typeMetadonnee.Id;
            }
            else
            {
                _logger.LogWarning("Type de métadonnée '{TypeNom}' non trouvé lors de la mise à jour", metadonneeDTO.TypeNom);
                // Utiliser un type par défaut si le type n'est pas trouvé
                var typeParDefaut = await _serviceTypeMetadonnee.ObtenirParNomAsync("Technique");
                if (typeParDefaut != null)
                {
                    metadonneeExistante.TypeId = typeParDefaut.Id;
                }
            }

            // Obtenir la catégorie de métadonnée par son nom
            if (!string.IsNullOrEmpty(metadonneeDTO.CategorieName))
            {
                var categories = await _serviceCategorieMetadonnee.ObtenirTousAsync();
                var categorieMetadonnee = categories.FirstOrDefault(c => c.Nom == metadonneeDTO.CategorieName);
                if (categorieMetadonnee != null)
                {
                    metadonneeExistante.CategorieId = categorieMetadonnee.Id;
                }
            }

            _logger.LogInformation("Mise à jour de la métadonnée avancée avec l'ID {Id}", id);
            await _serviceMetadonnee.MettreAJourAsync(metadonneeExistante);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour de la métadonnée avancée avec l'ID {Id}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, $"Une erreur est survenue lors de la mise à jour de la métadonnée avancée: {ex.Message}");
        }
    }

    /// <summary>
    /// Supprime une métadonnée avancée.
    /// </summary>
    /// <param name="id">Identifiant de la métadonnée avancée à supprimer.</param>
    /// <returns>Aucun contenu si la suppression est réussie.</returns>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Supprimer(Guid id)
    {
        try
        {
            var metadonneeExistante = await _serviceMetadonnee.ObtenirParIdAsync(id);
            if (metadonneeExistante == null)
            {
                _logger.LogWarning("Métadonnée avancée avec l'ID {Id} non trouvée lors de la suppression", id);
                return NotFound($"Métadonnée avancée avec l'ID {id} non trouvée");
            }

            _logger.LogInformation("Suppression de la métadonnée avancée avec l'ID {Id}", id);
            await _serviceMetadonnee.SupprimerAsync(id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de la métadonnée avancée avec l'ID {Id}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, $"Une erreur est survenue lors de la suppression de la métadonnée avancée: {ex.Message}");
        }
    }
}
