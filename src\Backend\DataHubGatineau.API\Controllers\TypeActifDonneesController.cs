using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Interfaces;
using DataHubGatineau.Infrastructure.Persistence;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// Contrôleur pour les types d'actifs de données.
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class TypeActifDonneesController : ControllerBase
{
    private readonly IDepotBase<TypeActifDonnees> _depot;
    private readonly CentreDonneesDbContext _context;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="TypeActifDonneesController"/>.
    /// </summary>
    /// <param name="depot">Le dépôt des types d'actifs de données.</param>
    /// <param name="context">Le contexte de base de données.</param>
    public TypeActifDonneesController(IDepotBase<TypeActifDonnees> depot, CentreDonneesDbContext context)
    {
        _depot = depot;
        _context = context;
    }

    /// <summary>
    /// Obtient tous les types d'actifs de données.
    /// </summary>
    /// <returns>Les types d'actifs de données.</returns>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<TypeActifDonnees>>> ObtenirTous()
    {
        var types = await _context.TypesActifDonnees
            .Include(t => t.Categorie)
            .ToListAsync();
        return Ok(types);
    }

    /// <summary>
    /// Obtient un type d'actif de données par son identifiant.
    /// </summary>
    /// <param name="id">L'identifiant du type d'actif de données.</param>
    /// <returns>Le type d'actif de données.</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<TypeActifDonnees>> ObtenirParId(Guid id)
    {
        var type = await _context.TypesActifDonnees
            .Include(t => t.Categorie)
            .FirstOrDefaultAsync(t => t.Id == id);
        if (type == null)
        {
            return NotFound();
        }

        return Ok(type);
    }

    /// <summary>
    /// Crée un nouveau type d'actif de données.
    /// </summary>
    /// <param name="type">Le type d'actif de données à créer.</param>
    /// <returns>Le type d'actif de données créé.</returns>
    [HttpPost]
    public async Task<ActionResult<TypeActifDonnees>> Creer(TypeActifDonnees type)
    {
        await _depot.AjouterAsync(type);
        return CreatedAtAction(nameof(ObtenirParId), new { id = type.Id }, type);
    }

    /// <summary>
    /// Met à jour un type d'actif de données.
    /// </summary>
    /// <param name="id">L'identifiant du type d'actif de données.</param>
    /// <param name="type">Le type d'actif de données à mettre à jour.</param>
    /// <returns>Aucun contenu.</returns>
    [HttpPut("{id}")]
    public async Task<IActionResult> MettreAJour(Guid id, TypeActifDonnees type)
    {
        if (id != type.Id)
        {
            return BadRequest();
        }

        await _depot.MettreAJourAsync(type);
        return NoContent();
    }

    /// <summary>
    /// Supprime un type d'actif de données.
    /// </summary>
    /// <param name="id">L'identifiant du type d'actif de données.</param>
    /// <returns>Aucun contenu.</returns>
    [HttpDelete("{id}")]
    public async Task<IActionResult> Supprimer(Guid id)
    {
        var type = await _depot.ObtenirParIdAsync(id);
        if (type == null)
        {
            return NotFound();
        }

        await _depot.SupprimerAsync(id);
        return NoContent();
    }

    /// <summary>
    /// Initialise les types d'actifs spécifiques inspirés de Collibra.
    /// </summary>
    /// <returns>Le nombre de types créés.</returns>
    [HttpPost("initialiser-types-specifiques")]
    public async Task<ActionResult<int>> InitialiserTypesSpecifiques()
    {
        var typesSpecifiques = new List<TypeActifDonnees>
        {
            // Types d'actifs technologiques
            new TypeActifDonnees
            {
                Id = Guid.NewGuid(),
                Nom = "Table",
                Description = "Une implémentation d'entités de données en colonnes et lignes, dans un système de base de données donné. C'est la structure de base d'une base de données relationnelle.",
                CreePar = "Système",
                ModifiePar = "Système",
                DateCreation = DateTime.Now,
                DateModification = DateTime.Now
            },
            new TypeActifDonnees
            {
                Id = Guid.NewGuid(),
                Nom = "Colonne",
                Description = "Une unité atomique de données qui peut être stockée dans une table de base de données. Représente un attribut spécifique d'une entité.",
                CreePar = "Système",
                ModifiePar = "Système",
                DateCreation = DateTime.Now,
                DateModification = DateTime.Now
            },
            new TypeActifDonnees
            {
                Id = Guid.NewGuid(),
                Nom = "Schéma",
                Description = "Une structure organisée décrite dans un langage formel qui définit les objets dans les actifs technologiques (tables et colonnes dans une base de données relationnelle).",
                CreePar = "Système",
                ModifiePar = "Système",
                DateCreation = DateTime.Now,
                DateModification = DateTime.Now
            },
            new TypeActifDonnees
            {
                Id = Guid.NewGuid(),
                Nom = "Vue Base de Données",
                Description = "Une vue de base de données est une table virtuelle basée sur le résultat d'une instruction SQL. Elle contient des lignes et des colonnes comme une vraie table.",
                CreePar = "Système",
                ModifiePar = "Système",
                DateCreation = DateTime.Now,
                DateModification = DateTime.Now
            },
            // Types d'actifs logiques/conceptuels
            new TypeActifDonnees
            {
                Id = Guid.NewGuid(),
                Nom = "Entité de Données",
                Description = "Une unité de données qui peut être classifiée et qui peut avoir une relation déclarée avec d'autres unités de données. Exemples: Client, Employé, Produit.",
                CreePar = "Système",
                ModifiePar = "Système",
                DateCreation = DateTime.Now,
                DateModification = DateTime.Now
            },
            new TypeActifDonnees
            {
                Id = Guid.NewGuid(),
                Nom = "Attribut de Données",
                Description = "Une spécification qui définit une propriété d'une entité de données. Exemples: DateNaissanceClient, PrénomEmployé.",
                CreePar = "Système",
                ModifiePar = "Système",
                DateCreation = DateTime.Now,
                DateModification = DateTime.Now
            },
            new TypeActifDonnees
            {
                Id = Guid.NewGuid(),
                Nom = "Modèle de Données",
                Description = "Un diagramme qui organise les éléments de données et standardise la façon dont les éléments de données se rapportent les uns aux autres.",
                CreePar = "Système",
                ModifiePar = "Système",
                DateCreation = DateTime.Now,
                DateModification = DateTime.Now
            },
            new TypeActifDonnees
            {
                Id = Guid.NewGuid(),
                Nom = "Ensemble de Données",
                Description = "Une collection d'ensembles de données liés qui sont des éléments de données ou composés d'éléments de données.",
                CreePar = "Système",
                ModifiePar = "Système",
                DateCreation = DateTime.Now,
                DateModification = DateTime.Now
            },
            new TypeActifDonnees
            {
                Id = Guid.NewGuid(),
                Nom = "Élément de Données",
                Description = "Une construction qui documente les aspects de quelque chose d'abstrait, en particulier celui qui est essentiel pour les affaires.",
                CreePar = "Système",
                ModifiePar = "Système",
                DateCreation = DateTime.Now,
                DateModification = DateTime.Now
            },
            // Types d'actifs métier
            new TypeActifDonnees
            {
                Id = Guid.NewGuid(),
                Nom = "Terme Métier",
                Description = "Un mot ou une phrase qui décrit un concept utilisé dans une branche particulière des affaires.",
                CreePar = "Système",
                ModifiePar = "Système",
                DateCreation = DateTime.Now,
                DateModification = DateTime.Now
            },
            new TypeActifDonnees
            {
                Id = Guid.NewGuid(),
                Nom = "KPI",
                Description = "Indicateur clé de performance, un indicateur pour mesurer périodiquement le succès ou les progrès vers un objectif stratégique d'une activité particulière ou d'une organisation.",
                CreePar = "Système",
                ModifiePar = "Système",
                DateCreation = DateTime.Now,
                DateModification = DateTime.Now
            }
        };

        // Verificar si los tipos ya existen para evitar duplicados
        var typesExistants = await _depot.ObtenirTousAsync();
        var nomsExistants = typesExistants.Select(t => t.Nom).ToHashSet();

        var typesACreer = typesSpecifiques.Where(t => !nomsExistants.Contains(t.Nom)).ToList();

        foreach (var type in typesACreer)
        {
            await _depot.AjouterAsync(type);
        }

        return Ok(new {
            message = $"{typesACreer.Count} nouveaux types d'actifs spécifiques créés avec succès!",
            typesCreés = typesACreer.Select(t => t.Nom).ToList()
        });
    }

    /// <summary>
    /// Initialise les catégories de types d'actifs et associe les types existants.
    /// </summary>
    /// <returns>Le nombre de catégories créées.</returns>
    [HttpPost("initialiser-categories")]
    public async Task<ActionResult<int>> InitialiserCategories()
    {
        // Créer les catégories principales
        var categories = new List<CategorieTypeActifDonnees>
        {
            new CategorieTypeActifDonnees
            {
                Id = Guid.NewGuid(),
                Nom = "Actifs Métier",
                Description = "Type d'actif utilisé exclusivement et gouverné par la communauté des utilisateurs métier. Inclut les concepts métier comme les termes métier, processus métier, etc.",
                Icone = "business_asset",
                Couleur = "#4CAF50",
                Ordre = 1,
                CreePar = "Système",
                ModifiePar = "Système",
                DateCreation = DateTime.Now,
                DateModification = DateTime.Now
            },
            new CategorieTypeActifDonnees
            {
                Id = Guid.NewGuid(),
                Nom = "Actifs de Données",
                Description = "Type d'actif qui représente les détails des données organisationnelles en deux couches : indépendante de toute technologie particulière et prenant en compte le système d'implémentation.",
                Icone = "data_asset",
                Couleur = "#2196F3",
                Ordre = 2,
                CreePar = "Système",
                ModifiePar = "Système",
                DateCreation = DateTime.Now,
                DateModification = DateTime.Now
            },
            new CategorieTypeActifDonnees
            {
                Id = Guid.NewGuid(),
                Nom = "Actifs Technologiques",
                Description = "Élément de technologie de l'information (matériel, logiciel, base de données, plateforme logicielle) qui aide une organisation à exécuter une application métier.",
                Icone = "technology_asset",
                Couleur = "#FF9800",
                Ordre = 3,
                CreePar = "Système",
                ModifiePar = "Système",
                DateCreation = DateTime.Now,
                DateModification = DateTime.Now
            },
            new CategorieTypeActifDonnees
            {
                Id = Guid.NewGuid(),
                Nom = "Actifs de Gouvernance",
                Description = "Type d'actif utilisé pour surveiller et préconiser la maximisation des performances ou de l'utilisation d'autres actifs métier et de données tout en minimisant les facteurs de risque.",
                Icone = "governance_asset",
                Couleur = "#9C27B0",
                Ordre = 4,
                CreePar = "Système",
                ModifiePar = "Système",
                DateCreation = DateTime.Now,
                DateModification = DateTime.Now
            }
        };

        // Ajouter les catégories à la base de données
        foreach (var categorie in categories)
        {
            await _context.CategoriesTypesActifDonnees.AddAsync(categorie);
        }
        await _context.SaveChangesAsync();

        // Associer les types existants aux catégories appropriées
        var typesExistants = await _depot.ObtenirTousAsync();
        var categorieMetier = categories.First(c => c.Nom == "Actifs Métier");
        var categorieDonnees = categories.First(c => c.Nom == "Actifs de Données");
        var categorieTechnologique = categories.First(c => c.Nom == "Actifs Technologiques");

        foreach (var type in typesExistants)
        {
            // Associer selon le nom du type
            if (type.Nom == "Terme Métier" || type.Nom == "KPI")
            {
                type.CategorieId = categorieMetier.Id;
            }
            else if (type.Nom == "Table" || type.Nom == "Colonne" || type.Nom == "Entité de Données" ||
                     type.Nom == "Attribut de Données" || type.Nom == "Modèle de Données" ||
                     type.Nom == "Ensemble de Données" || type.Nom == "Élément de Données")
            {
                type.CategorieId = categorieDonnees.Id;
            }
            else if (type.Nom == "Schéma" || type.Nom == "Vue Base de Données" || type.Nom == "Base de données" ||
                     type.Nom == "Fichier" || type.Nom == "API" || type.Nom == "Serveur")
            {
                type.CategorieId = categorieTechnologique.Id;
            }
            else
            {
                // Par défaut, assigner à Actifs de Données
                type.CategorieId = categorieDonnees.Id;
            }

            type.DateModification = DateTime.Now;
            type.ModifiePar = "Système";
        }

        await _context.SaveChangesAsync();

        return Ok(new {
            message = $"{categories.Count} catégories créées et {typesExistants.Count()} types associés avec succès!",
            catégories = categories.Select(c => c.Nom).ToList()
        });
    }

    /// <summary>
    /// Nettoie les types d'actifs de données dupliqués en conservant le plus récent en utilisant SQL direct.
    /// </summary>
    /// <returns>Le nombre de doublons supprimés.</returns>
    [HttpPost("nettoyer-doublons")]
    public async Task<ActionResult<int>> NettoyerDoublons()
    {
        try
        {
            // Utiliser SQL direct pour nettoyer les doublons
            var scriptSql = @"
                WITH DoublonsClasses AS (
                    SELECT
                        Id,
                        Nom,
                        ROW_NUMBER() OVER (PARTITION BY Nom ORDER BY DateCreation DESC, DateModification DESC) as NumeroRang
                    FROM [Metadonnees].[TypesActifDonnees]
                )
                DELETE FROM [Metadonnees].[TypesActifDonnees]
                WHERE Id IN (
                    SELECT Id
                    FROM DoublonsClasses
                    WHERE NumeroRang > 1
                );";

            var doublonsSupprimes = await _context.Database.ExecuteSqlRawAsync(scriptSql);

            return Ok(new {
                message = $"{doublonsSupprimes} types d'actifs dupliqués supprimés avec succès!",
                sqlExecute = true
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new {
                message = "Erreur lors du nettoyage des doublons",
                erreur = ex.Message
            });
        }
    }
}
