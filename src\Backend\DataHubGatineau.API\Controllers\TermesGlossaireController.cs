﻿using Asp.Versioning;
using DataHubGatineau.Application.Interfaces;
using DataHubGatineau.Domain.Entites;
using Microsoft.AspNetCore.Mvc;

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// Contrôleur pour les opérations sur les termes du glossaire.
/// </summary>
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/TermesGlossaire")]
public class TermesGlossaireController : ApiControllerBase
{
    private readonly IServiceTermeGlossaire _serviceTermeGlossaire;

    public TermesGlossaireController(IServiceTermeGlossaire serviceTermeGlossaire)
    {
        _serviceTermeGlossaire = serviceTermeGlossaire;
    }

    // --- Associations TermeGlossaire ↔ ActifDonnees ---

    /// <summary>
    /// Obtient les termes du glossaire associés à un actif de données.
    /// </summary>
    [HttpGet("actif/{actifDonneesId:guid}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossaire>>> ObtenirParActifDonnees(Guid actifDonneesId)
    {
        var termes = await _serviceTermeGlossaire.ObtenirParActifDonneesAsync(actifDonneesId);
        return Ok(termes);
    }

    /// <summary>
    /// Associe un terme du glossaire à un actif de données.
    /// </summary>
    [HttpPost("{termeId:guid}/actifs/{actifDonneesId:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> AssocierActifDonnees(Guid termeId, Guid actifDonneesId)
    {
        var ok = await _serviceTermeGlossaire.AssocierActifDonneesAsync(termeId, actifDonneesId);
        if (!ok) return NotFound();
        return NoContent();
    }

    /// <summary>
    /// Dissocie un terme du glossaire d'un actif de données.
    /// </summary>
    [HttpDelete("{termeId:guid}/actifs/{actifDonneesId:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DissocierActifDonnees(Guid termeId, Guid actifDonneesId)
    {
        var ok = await _serviceTermeGlossaire.DissocierActifDonneesAsync(termeId, actifDonneesId);
        if (!ok) return NotFound();
        return NoContent();
    }

    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossaire>>> ObtenirTous()
    {
        var termes = await _serviceTermeGlossaire.ObtenirTousAsync();
        return Ok(termes);
    }

    [HttpGet("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<TermeGlossaire>> ObtenirParId(Guid id)
    {
        var terme = await _serviceTermeGlossaire.ObtenirParIdAsync(id);
        if (terme == null)
        {
            return NotFound();
        }
        return Ok(terme);
    }

    [HttpGet("domaine/{domaineAffaires}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossaire>>> ObtenirParDomaineAffaires(string domaineAffaires)
    {
        var termes = await _serviceTermeGlossaire.ObtenirParDomaineAffairesAsync(domaineAffaires);
        return Ok(termes);
    }

    [HttpGet("proprietaire/{proprietaire}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossaire>>> ObtenirParProprietaire(string proprietaire)
    {
        var termes = await _serviceTermeGlossaire.ObtenirParProprietaireAsync(proprietaire);
        return Ok(termes);
    }

    [HttpGet("{termeParentId}/enfants")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<IEnumerable<TermeGlossaire>>> ObtenirTermesEnfants(Guid termeParentId)
    {
        var termeParent = await _serviceTermeGlossaire.ObtenirParIdAsync(termeParentId);
        if (termeParent == null)
        {
            return NotFound();
        }
        var termesEnfants = await _serviceTermeGlossaire.ObtenirTermesEnfantsAsync(termeParentId);
        return Ok(termesEnfants);
    }

    [HttpGet("racines")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossaire>>> ObtenirTermesRacines()
    {
        var termesRacines = await _serviceTermeGlossaire.ObtenirTermesRacinesAsync();
        return Ok(termesRacines);
    }

    [HttpGet("recherche")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossaire>>> Rechercher([FromQuery] string motCle)
    {
        var termes = await _serviceTermeGlossaire.RechercherAsync(motCle);
        return Ok(termes);
    }

    [HttpPost]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<TermeGlossaire>> Ajouter(TermeGlossaire terme)
    {
        if (terme == null)
        {
            return BadRequest();
        }
        var utilisateurIdClaim = User.Claims.FirstOrDefault(c => c.Type == "sub" || c.Type == "userid" || c.Type.EndsWith("/nameidentifier"));
        var roleClaim = User.Claims.FirstOrDefault(c => c.Type == "role" || c.Type == "roles" || c.Type.EndsWith("/role"));
        if (utilisateurIdClaim == null || roleClaim == null)
        {
            return Unauthorized("Impossible de déterminer l'utilisateur ou le rôle depuis le token.");
        }
        Guid utilisateurId;
        if (!Guid.TryParse(utilisateurIdClaim.Value, out utilisateurId))
        {
            return Unauthorized("Identifiant utilisateur invalide.");
        }
        var roleUtilisateur = roleClaim.Value;
        var termeAjoute = await _serviceTermeGlossaire.AjouterAsync(terme, utilisateurId, roleUtilisateur);
        return CreatedAtAction(nameof(ObtenirParId), new { id = termeAjoute.Id }, termeAjoute);
    }

    [HttpPut("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> MettreAJour(Guid id, TermeGlossaire terme)
    {
        if (terme == null || id != terme.Id)
        {
            return BadRequest();
        }
        var termeExistant = await _serviceTermeGlossaire.ObtenirParIdAsync(id);
        if (termeExistant == null)
        {
            return NotFound();
        }
        var utilisateurIdClaim = User.Claims.FirstOrDefault(c => c.Type == "sub" || c.Type == "userid" || c.Type.EndsWith("/nameidentifier"));
        var roleClaim = User.Claims.FirstOrDefault(c => c.Type == "role" || c.Type == "roles" || c.Type.EndsWith("/role"));
        if (utilisateurIdClaim == null || roleClaim == null)
        {
            return Unauthorized("Impossible de déterminer l'utilisateur ou le rôle depuis le token.");
        }
        Guid utilisateurId;
        if (!Guid.TryParse(utilisateurIdClaim.Value, out utilisateurId))
        {
            return Unauthorized("Identifiant utilisateur invalide.");
        }
        var roleUtilisateur = roleClaim.Value;
        await _serviceTermeGlossaire.MettreAJourAsync(terme, utilisateurId, roleUtilisateur);
        return NoContent();
    }

    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Supprimer(Guid id)
    {
        var termeExistant = await _serviceTermeGlossaire.ObtenirParIdAsync(id);
        if (termeExistant == null)
        {
            return NotFound();
        }
        await _serviceTermeGlossaire.SupprimerAsync(id);
        return NoContent();
    }
    }
