using DataHubGatineau.Application.DTOs.Identity;
using DataHubGatineau.Application.Services.Identity.Interfaces;
using DataHubGatineau.Domain.Entites.Identity;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
// ...existing code...
// using DataHubGatineau.Core.Constants; // Remplacé par des stratégies d'autorisation dynamiques basées BD

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// Contrôleur pour la gestion des demandes d'accès.
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class DemandesAccesController : ControllerBase
{
    private readonly IServiceDemandeAcces _serviceDemandeAcces;
    private readonly ILogger<DemandesAccesController> _logger;
    private readonly IAuthorizationService _authorizationService;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="DemandesAccesController"/>.
    /// </summary>
    /// <param name="serviceDemandeAcces">Service de gestion des demandes d'accès.</param>
    /// <param name="logger">Logger.</param>
    public DemandesAccesController(IServiceDemandeAcces serviceDemandeAcces, ILogger<DemandesAccesController> logger, IAuthorizationService authorizationService)
    {
        _serviceDemandeAcces = serviceDemandeAcces;
        _logger = logger;
        _authorizationService = authorizationService;
    }

    /// <summary>
    /// Obtient toutes les demandes d'accès.
    /// </summary>
    /// <returns>Liste des demandes d'accès.</returns>
    [HttpGet]
    [Authorize(Policy = "Role:AdministrateurSysteme")]
    public async Task<ActionResult<IEnumerable<DemandeAccesDTO>>> ObtenirToutes()
    {
        try
        {
            var demandes = await _serviceDemandeAcces.ObtenirToutesAsync();
            return Ok(demandes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de toutes les demandes d'accès");
            return StatusCode(500, "Une erreur est survenue lors de la récupération des demandes d'accès");
        }
    }

    /// <summary>
    /// Obtient une demande d'accès par son identifiant.
    /// </summary>
    /// <param name="id">Identifiant de la demande d'accès.</param>
    /// <returns>La demande d'accès correspondante.</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<DemandeAccesDTO>> ObtenirParId(Guid id)
    {
        try
        {
            var demande = await _serviceDemandeAcces.ObtenirParIdAsync(id);
            if (demande == null)
            {
                return NotFound($"Demande d'accès avec l'ID {id} non trouvée");
            }

            // Vérifier si l'utilisateur est autorisé à voir cette demande
            var utilisateurId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(utilisateurId) || !Guid.TryParse(utilisateurId, out var id_utilisateur))
            {
                return Unauthorized("Utilisateur non authentifié");
            }

            // Seuls l'administrateur, le demandeur et l'approbateur peuvent voir la demande
            var autorisationAdmin = await _authorizationService.AuthorizeAsync(User, null, "Role:AdministrateurSysteme");
            if (!autorisationAdmin.Succeeded && 
                demande.DemandeurId != id_utilisateur && 
                demande.ApprobateurId != id_utilisateur)
            {
                return Forbid("Vous n'êtes pas autorisé à voir cette demande d'accès");
            }

            return Ok(demande);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de la demande d'accès avec l'ID {DemandeId}", id);
            return StatusCode(500, "Une erreur est survenue lors de la récupération de la demande d'accès");
        }
    }

    /// <summary>
    /// Obtient les demandes d'accès de l'utilisateur actuel.
    /// </summary>
    /// <returns>Liste des demandes d'accès de l'utilisateur actuel.</returns>
    [HttpGet("mes-demandes")]
    public async Task<ActionResult<IEnumerable<DemandeAccesDTO>>> ObtenirMesDemandes()
    {
        try
        {
            var utilisateurId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(utilisateurId) || !Guid.TryParse(utilisateurId, out var id))
            {
                return Unauthorized("Utilisateur non authentifié");
            }

            var demandes = await _serviceDemandeAcces.ObtenirParDemandeurAsync(id);
            return Ok(demandes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des demandes d'accès de l'utilisateur actuel");
            return StatusCode(500, "Une erreur est survenue lors de la récupération des demandes d'accès");
        }
    }

    /// <summary>
    /// Obtient les demandes d'accès à approuver par l'utilisateur actuel.
    /// </summary>
    /// <returns>Liste des demandes d'accès à approuver par l'utilisateur actuel.</returns>
    [HttpGet("a-approuver")]
    [Authorize(Policy = "Role:AdministrateurSysteme")]
    public async Task<ActionResult<IEnumerable<DemandeAccesDTO>>> ObtenirDemandesAApprouver()
    {
        try
        {
            var utilisateurId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(utilisateurId) || !Guid.TryParse(utilisateurId, out var id))
            {
                return Unauthorized("Utilisateur non authentifié");
            }

            var demandes = await _serviceDemandeAcces.ObtenirParStatutAsync(StatutDemandeAcces.EnAttente);
            return Ok(demandes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des demandes d'accès à approuver");
            return StatusCode(500, "Une erreur est survenue lors de la récupération des demandes d'accès");
        }
    }

    /// <summary>
    /// Crée une nouvelle demande d'accès.
    /// </summary>
    /// <param name="demande">Données de la demande d'accès à créer.</param>
    /// <returns>La demande d'accès créée.</returns>
    [HttpPost]
    public async Task<ActionResult<DemandeAccesDTO>> Creer([FromBody] DemandeAccesCreationDTO demande)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Vérifier si l'utilisateur est autorisé à créer cette demande
            var utilisateurId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(utilisateurId) || !Guid.TryParse(utilisateurId, out var id))
            {
                return Unauthorized("Utilisateur non authentifié");
            }

            // Seul l'administrateur peut créer une demande pour un autre utilisateur
            var autorisationAdmin = await _authorizationService.AuthorizeAsync(User, null, "Role:AdministrateurSysteme");
            if (demande.DemandeurId != id && !autorisationAdmin.Succeeded)
            {
                return Forbid("Vous n'êtes pas autorisé à créer une demande pour un autre utilisateur");
            }

            var nouvelleDemande = await _serviceDemandeAcces.CreerAsync(demande);
            return CreatedAtAction(nameof(ObtenirParId), new { id = nouvelleDemande.Id }, nouvelleDemande);
        }
        catch (KeyNotFoundException ex)
        {
            _logger.LogWarning(ex, "Entité non trouvée lors de la création de la demande d'accès");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de la demande d'accès");
            return StatusCode(500, "Une erreur est survenue lors de la création de la demande d'accès");
        }
    }

    /// <summary>
    /// Approuve une demande d'accès.
    /// </summary>
    /// <param name="id">Identifiant de la demande d'accès.</param>
    /// <param name="commentaires">Commentaires de l'approbateur.</param>
    /// <returns>Résultat de l'approbation.</returns>
    [HttpPost("{id}/approuver")]
    [Authorize(Policy = "Role:AdministrateurSysteme")]
    public async Task<ActionResult> Approuver(Guid id, [FromBody] string? commentaires = null)
    {
        try
        {
            var utilisateurId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(utilisateurId) || !Guid.TryParse(utilisateurId, out var id_utilisateur))
            {
                return Unauthorized("Utilisateur non authentifié");
            }

            var resultat = await _serviceDemandeAcces.ApprouverDemandeAsync(id, id_utilisateur, commentaires);
            if (!resultat)
            {
                return BadRequest("Impossible d'approuver la demande d'accès");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'approbation de la demande d'accès avec l'ID {DemandeId}", id);
            return StatusCode(500, "Une erreur est survenue lors de l'approbation de la demande d'accès");
        }
    }

    /// <summary>
    /// Rejette une demande d'accès.
    /// </summary>
    /// <param name="id">Identifiant de la demande d'accès.</param>
    /// <param name="commentaires">Commentaires de l'approbateur.</param>
    /// <returns>Résultat du rejet.</returns>
    [HttpPost("{id}/rejeter")]
    [Authorize(Policy = "Role:AdministrateurSysteme")]
    public async Task<ActionResult> Rejeter(Guid id, [FromBody] string? commentaires = null)
    {
        try
        {
            var utilisateurId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(utilisateurId) || !Guid.TryParse(utilisateurId, out var id_utilisateur))
            {
                return Unauthorized("Utilisateur non authentifié");
            }

            var resultat = await _serviceDemandeAcces.RejeterDemandeAsync(id, id_utilisateur, commentaires);
            if (!resultat)
            {
                return BadRequest("Impossible de rejeter la demande d'accès");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du rejet de la demande d'accès avec l'ID {DemandeId}", id);
            return StatusCode(500, "Une erreur est survenue lors du rejet de la demande d'accès");
        }
    }

    /// <summary>
    /// Annule une demande d'accès.
    /// </summary>
    /// <param name="id">Identifiant de la demande d'accès.</param>
    /// <returns>Résultat de l'annulation.</returns>
    [HttpPost("{id}/annuler")]
    public async Task<ActionResult> Annuler(Guid id)
    {
        try
        {
            var utilisateurId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(utilisateurId) || !Guid.TryParse(utilisateurId, out var id_utilisateur))
            {
                return Unauthorized("Utilisateur non authentifié");
            }

            // Vérifier si l'utilisateur est autorisé à annuler cette demande
            var demande = await _serviceDemandeAcces.ObtenirParIdAsync(id);
            if (demande == null)
            {
                return NotFound($"Demande d'accès avec l'ID {id} non trouvée");
            }

            // Seuls l'administrateur et le demandeur peuvent annuler la demande
            var autorisationAdmin = await _authorizationService.AuthorizeAsync(User, null, "Role:AdministrateurSysteme");
            if (demande.DemandeurId != id_utilisateur && !autorisationAdmin.Succeeded)
            {
                return Forbid("Vous n'êtes pas autorisé à annuler cette demande d'accès");
            }

            var resultat = await _serviceDemandeAcces.AnnulerDemandeAsync(id, id_utilisateur);
            if (!resultat)
            {
                return BadRequest("Impossible d'annuler la demande d'accès");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'annulation de la demande d'accès avec l'ID {DemandeId}", id);
            return StatusCode(500, "Une erreur est survenue lors de l'annulation de la demande d'accès");
        }
    }
}
