using DataHubGatineau.Application.DTOs;
using DataHubGatineau.Application.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// Contrôleur pour la gestion des historiques de scan.
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class HistoriqueScanController : ControllerBase
{
    private readonly IServiceHistoriqueScan _serviceHistoriqueScan;
    private readonly ILogger<HistoriqueScanController> _logger;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="HistoriqueScanController"/>.
    /// </summary>
    /// <param name="serviceHistoriqueScan">Le service d'historique de scan.</param>
    /// <param name="logger">Le logger.</param>
    public HistoriqueScanController(IServiceHistoriqueScan serviceHistoriqueScan, ILogger<HistoriqueScanController> logger)
    {
        _serviceHistoriqueScan = serviceHistoriqueScan;
        _logger = logger;
    }

    /// <summary>
    /// Obtient tous les historiques de scan.
    /// </summary>
    /// <returns>Une collection d'historiques de scan.</returns>
    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<IEnumerable<HistoriqueScanDTO>>> ObtenirTous()
    {
        try
        {
            var historiquesScans = await _serviceHistoriqueScan.ObtenirTousAsync();
            return Ok(historiquesScans);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention de tous les historiques de scan");
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de l'obtention des historiques de scan");
        }
    }

    /// <summary>
    /// Obtient un historique de scan par son identifiant.
    /// </summary>
    /// <param name="id">L'identifiant de l'historique de scan.</param>
    /// <returns>L'historique de scan correspondant.</returns>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<HistoriqueScanDTO>> ObtenirParId(Guid id)
    {
        try
        {
            var historiqueScan = await _serviceHistoriqueScan.ObtenirParIdAsync(id);
            if (historiqueScan == null)
            {
                return NotFound($"Historique de scan avec l'ID {id} non trouvé");
            }
            
            return Ok(historiqueScan);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention de l'historique de scan avec l'ID {Id}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de l'obtention de l'historique de scan");
        }
    }

    /// <summary>
    /// Obtient les historiques de scan pour une configuration de scan.
    /// </summary>
    /// <param name="configurationScanId">L'identifiant de la configuration de scan.</param>
    /// <returns>Une collection d'historiques de scan pour la configuration spécifiée.</returns>
    [HttpGet("parConfiguration/{configurationScanId:guid}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<IEnumerable<HistoriqueScanDTO>>> ObtenirParConfigurationScanId(Guid configurationScanId)
    {
        try
        {
            var historiquesScans = await _serviceHistoriqueScan.ObtenirParConfigurationScanIdAsync(configurationScanId);
            return Ok(historiquesScans);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention des historiques de scan pour la configuration de scan avec l'ID {Id}", configurationScanId);
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de l'obtention des historiques de scan");
        }
    }

    /// <summary>
    /// Obtient les historiques de scan pour une période donnée.
    /// </summary>
    /// <param name="dateDebut">La date de début de la période.</param>
    /// <param name="dateFin">La date de fin de la période.</param>
    /// <returns>Une collection d'historiques de scan pour la période spécifiée.</returns>
    [HttpGet("parPeriode")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<IEnumerable<HistoriqueScanDTO>>> ObtenirParPeriode([FromQuery] DateTime dateDebut, [FromQuery] DateTime dateFin)
    {
        try
        {
            if (dateDebut > dateFin)
            {
                return BadRequest("La date de début doit être antérieure ou égale à la date de fin");
            }
            
            var historiquesScans = await _serviceHistoriqueScan.ObtenirParPeriodeAsync(dateDebut, dateFin);
            return Ok(historiquesScans);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention des historiques de scan pour la période du {DateDebut} au {DateFin}", dateDebut, dateFin);
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de l'obtention des historiques de scan");
        }
    }

    /// <summary>
    /// Obtient les historiques de scan réussis.
    /// </summary>
    /// <returns>Une collection d'historiques de scan réussis.</returns>
    [HttpGet("reussis")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<IEnumerable<HistoriqueScanDTO>>> ObtenirHistoriquesReussis()
    {
        try
        {
            var historiquesScans = await _serviceHistoriqueScan.ObtenirHistoriquesReussisAsync();
            return Ok(historiquesScans);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention des historiques de scan réussis");
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de l'obtention des historiques de scan réussis");
        }
    }

    /// <summary>
    /// Obtient les historiques de scan échoués.
    /// </summary>
    /// <returns>Une collection d'historiques de scan échoués.</returns>
    [HttpGet("echoues")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<IEnumerable<HistoriqueScanDTO>>> ObtenirHistoriquesEchoues()
    {
        try
        {
            var historiquesScans = await _serviceHistoriqueScan.ObtenirHistoriquesEchouesAsync();
            return Ok(historiquesScans);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention des historiques de scan échoués");
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de l'obtention des historiques de scan échoués");
        }
    }

    /// <summary>
    /// Crée un nouvel historique de scan.
    /// </summary>
    /// <param name="historiqueScanDTO">Les données de l'historique de scan à créer.</param>
    /// <returns>L'historique de scan créé.</returns>
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<HistoriqueScanDTO>> Creer([FromBody] HistoriqueScanDTO historiqueScanDTO)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            
            var nouvelHistoriqueScan = await _serviceHistoriqueScan.CreerAsync(historiqueScanDTO);
            return CreatedAtAction(nameof(ObtenirParId), new { id = nouvelHistoriqueScan.Id }, nouvelHistoriqueScan);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création d'un historique de scan");
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de la création de l'historique de scan");
        }
    }

    /// <summary>
    /// Met à jour un historique de scan existant.
    /// </summary>
    /// <param name="id">L'identifiant de l'historique de scan à mettre à jour.</param>
    /// <param name="historiqueScanDTO">Les nouvelles données de l'historique de scan.</param>
    /// <returns>L'historique de scan mis à jour.</returns>
    [HttpPut("{id:guid}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<HistoriqueScanDTO>> MettreAJour(Guid id, [FromBody] HistoriqueScanDTO historiqueScanDTO)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            
            if (id != historiqueScanDTO.Id)
            {
                return BadRequest("L'ID dans l'URL ne correspond pas à l'ID dans le corps de la requête");
            }
            
            try
            {
                var historiqueScanMisAJour = await _serviceHistoriqueScan.MettreAJourAsync(id, historiqueScanDTO);
                return Ok(historiqueScanMisAJour);
            }
            catch (KeyNotFoundException)
            {
                return NotFound($"Historique de scan avec l'ID {id} non trouvé");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour de l'historique de scan avec l'ID {Id}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de la mise à jour de l'historique de scan");
        }
    }

    /// <summary>
    /// Supprime un historique de scan.
    /// </summary>
    /// <param name="id">L'identifiant de l'historique de scan à supprimer.</param>
    /// <returns>Un code de statut indiquant le résultat de l'opération.</returns>
    [HttpDelete("{id:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> Supprimer(Guid id)
    {
        try
        {
            var resultat = await _serviceHistoriqueScan.SupprimerAsync(id);
            if (!resultat)
            {
                return NotFound($"Historique de scan avec l'ID {id} non trouvé");
            }
            
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de l'historique de scan avec l'ID {Id}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de la suppression de l'historique de scan");
        }
    }
}
