using DataHubGatineau.Application.DTOs;
using DataHubGatineau.Domain.Interfaces;
using DataHubGatineau.Domain.Entites;
using Microsoft.AspNetCore.Mvc;

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// Contrôleur pour les statistiques.
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class StatistiquesController : ControllerBase
{
    private readonly IDepotBase<ActifDonnees> _depotActifDonnees;
    private readonly IDepotBase<TermeGlossaire> _depotTermeGlossaire;
    private readonly IDepotBase<DomaineGouvernance> _depotDomaineGouvernance;
    private readonly IDepotBase<RegleQualite> _depotRegleQualite;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="StatistiquesController"/>.
    /// </summary>
    /// <param name="depotActifDonnees">Le dépôt des actifs de données.</param>
    /// <param name="depotTermeGlossaire">Le dépôt des termes du glossaire.</param>
    /// <param name="depotDomaineGouvernance">Le dépôt des domaines de gouvernance.</param>
    /// <param name="depotRegleQualite">Le dépôt des règles de qualité.</param>
    public StatistiquesController(
        IDepotBase<ActifDonnees> depotActifDonnees,
        IDepotBase<TermeGlossaire> depotTermeGlossaire,
        IDepotBase<DomaineGouvernance> depotDomaineGouvernance,
        IDepotBase<RegleQualite> depotRegleQualite)
    {
        _depotActifDonnees = depotActifDonnees;
        _depotTermeGlossaire = depotTermeGlossaire;
        _depotDomaineGouvernance = depotDomaineGouvernance;
        _depotRegleQualite = depotRegleQualite;
    }

    /// <summary>
    /// Obtient les statistiques générales.
    /// </summary>
    /// <returns>Les statistiques générales.</returns>
    [HttpGet("generales")]
    public async Task<ActionResult<StatistiquesDTO>> ObtenirStatistiquesGenerales()
    {
        var actifsDonnees = await _depotActifDonnees.ObtenirTousAsync();
        var termesGlossaire = await _depotTermeGlossaire.ObtenirTousAsync();
        var domainesGouvernance = await _depotDomaineGouvernance.ObtenirTousAsync();
        var reglesQualite = await _depotRegleQualite.ObtenirTousAsync();

        // Calculer la répartition des actifs par type
        var repartitionParType = new Dictionary<string, int>();
        var typesActifs = actifsDonnees
            .GroupBy(a => a.TypeActifDonneesId)
            .Select(g => new { TypeId = g.Key, Count = g.Count() });

        foreach (var type in typesActifs)
        {
            string typeName = type.TypeId?.ToString() ?? "Non défini";
            repartitionParType.Add(typeName, type.Count);
        }

        // Calculer la répartition des actifs par classification
        var repartitionParClassification = new Dictionary<string, int>();
        var classifications = actifsDonnees
            .GroupBy(a => a.ClassificationSensibilite)
            .Select(g => new { Classification = g.Key, Count = g.Count() });

        foreach (var classification in classifications)
        {
            string classificationName = classification.Classification.ToString();
            repartitionParClassification.Add(classificationName, classification.Count);
        }

        var statistiques = new StatistiquesDTO
        {
            NombreTotalElementsDonnees = actifsDonnees.Count(),
            NombreTotalTermesGlossaire = termesGlossaire.Count(),
            NombreTotalDomainesGouvernance = domainesGouvernance.Count(),
            NombreTotalReglesQualite = reglesQualite.Count(),
            // Pour les autres statistiques, nous utilisons des valeurs simulées pour le moment
            NombreResultatsSucces = 45,
            NombreResultatsAvertissement = 15,
            NombreResultatsEchec = 10,
            NombreResultatsErreur = 5,
            RepartitionElementsParType = repartitionParType,
            RepartitionElementsParClassification = repartitionParClassification,
            NombreElementsSansTerme = 0 // À implémenter plus tard
        };

        return Ok(statistiques);
    }
}
